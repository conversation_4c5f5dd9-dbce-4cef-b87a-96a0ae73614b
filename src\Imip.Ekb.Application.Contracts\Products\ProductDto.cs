using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// DTO for Product entity
    /// </summary>
    public class ProductDto : AuditedEntityDto<Guid>
    {
        /// <summary>
        /// Name of the product
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Description of the product
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Price of the product
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// Quantity in stock
        /// </summary>
        public int StockQuantity { get; set; }

        /// <summary>
        /// SKU (Stock Keeping Unit) - unique identifier for the product
        /// </summary>
        public string SKU { get; set; } = string.Empty;

        /// <summary>
        /// Whether the product is active and available for sale
        /// </summary>
        public bool IsActive { get; set; }
    }
}
