using System;
using System.Collections.Generic;
using System.Linq;
using Imip.Ekb.Employees;
using Riok.Mapperly.Abstractions;

namespace Imip.Ekb.Mapping;

/// <summary>
/// Mapperly mapper for Employee entity and related DTOs
/// Demonstrates complex mappings including computed properties and custom logic
/// </summary>
[Mapper]
public partial class EmployeeMapper : IAbpMapper<Employee, EmployeeDto, Guid, CreateUpdateEmployeeDto>
{
    /// <summary>
    /// Maps Employee entity to EmployeeDto
    /// Includes custom mapping for FullName computed property
    /// </summary>
    [MapProperty(nameof(Employee.FullName), nameof(EmployeeDto.FullName))]
    public partial EmployeeDto ToDto(Employee entity);

    /// <summary>
    /// Maps list of Employee entities to list of EmployeeDto
    /// </summary>
    public partial List<EmployeeDto> ToDto(List<Employee> entities);

    /// <summary>
    /// Creates a new Employee entity from CreateUpdateEmployeeDto
    /// Uses the entity constructor to properly initialize the entity
    /// </summary>
    public Employee ToEntity(CreateUpdateEmployeeDto createDto)
    {
        return new Employee(
            Guid.NewGuid(),
            createDto.FirstName,
            createDto.LastName,
            createDto.Email,
            createDto.Department,
            createDto.Position,
            createDto.HireDate,
            createDto.Salary,
            createDto.PhoneNumber,
            createDto.PhotoPath
        );
    }

    /// <summary>
    /// Creates a new Employee entity from CreateUpdateEmployeeDto with specified ID
    /// </summary>
    public Employee ToEntity(CreateUpdateEmployeeDto createDto, Guid id)
    {
        return new Employee(
            id,
            createDto.FirstName,
            createDto.LastName,
            createDto.Email,
            createDto.Department,
            createDto.Position,
            createDto.HireDate,
            createDto.Salary,
            createDto.PhoneNumber,
            createDto.PhotoPath
        );
    }

    /// <summary>
    /// Updates existing Employee entity with data from CreateUpdateEmployeeDto
    /// Preserves system properties and computed properties
    /// </summary>
    public void UpdateEntity(CreateUpdateEmployeeDto updateDto, Employee entity)
    {
        entity.UpdateDetails(
            updateDto.FirstName,
            updateDto.LastName,
            updateDto.Email,
            updateDto.Department,
            updateDto.Position,
            updateDto.HireDate,
            updateDto.Salary,
            updateDto.PhoneNumber,
            updateDto.PhotoPath
        );
    }

    /// <summary>
    /// Maps Employee to a summary DTO for dashboard views
    /// Demonstrates selective mapping with computed properties
    /// </summary>
    public EmployeeSummaryDto ToSummaryDto(Employee entity)
    {
        return new EmployeeSummaryDto
        {
            Id = entity.Id,
            FullName = entity.FullName,
            Department = entity.Department,
            Position = entity.Position,
            IsActive = entity.IsActive
        };
    }

    /// <summary>
    /// Maps list of employees to summary DTOs
    /// </summary>
    public List<EmployeeSummaryDto> ToSummaryDto(List<Employee> entities)
    {
        return entities.Select(ToSummaryDto).ToList();
    }

    /// <summary>
    /// Custom mapping method with business logic
    /// Demonstrates how to add custom calculations and formatting
    /// </summary>
    public EmployeeDetailDto ToDetailDto(Employee entity)
    {
        var dto = new EmployeeDetailDto
        {
            Id = entity.Id,
            FullName = entity.FullName,
            Email = entity.Email,
            PhoneNumber = entity.PhoneNumber,
            Department = entity.Department,
            Position = entity.Position ?? "Not Specified",
            HireDate = entity.HireDate,
            Salary = entity.Salary,
            IsActive = entity.IsActive,

            // Custom computed properties
            YearsOfService = CalculateYearsOfService(entity.HireDate),
            FormattedSalary = FormatSalary(entity.Salary),
            ContactInfo = FormatContactInfo(entity.Email, entity.PhoneNumber)
        };

        // Map auditing properties using the helper method
        AbpMapperExtensions.MapAuditingProperties(entity, dto);

        return dto;
    }

    /// <summary>
    /// Helper method to calculate years of service
    /// </summary>
    private static int CalculateYearsOfService(DateTime hireDate)
    {
        return AbpMapperExtensions.CalculateYearsOfService(hireDate);
    }

    /// <summary>
    /// Helper method to format salary
    /// </summary>
    private static string FormatSalary(decimal salary)
    {
        return AbpMapperExtensions.FormatSalary(salary);
    }

    /// <summary>
    /// Helper method to format contact information
    /// </summary>
    private static string FormatContactInfo(string email, string? phoneNumber)
    {
        return AbpMapperExtensions.FormatContactInfo(email, phoneNumber);
    }
}

/// <summary>
/// Summary DTO for employee list views
/// </summary>
public class EmployeeSummaryDto
{
    public Guid Id { get; set; }
    public string FullName { get; set; } = string.Empty;
    public string Department { get; set; } = string.Empty;
    public string? Position { get; set; }
    public bool IsActive { get; set; }
}

/// <summary>
/// Detailed DTO with computed properties
/// </summary>
public class EmployeeDetailDto : EmployeeDto
{
    public int YearsOfService { get; set; }
    public string FormattedSalary { get; set; } = string.Empty;
    public string ContactInfo { get; set; } = string.Empty;
}
