using System;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Auditing;
using Volo.Abp.Domain.Entities.Auditing;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Users;

namespace Imip.Ekb.Mapping;

/// <summary>
/// Extension methods for handling ABP-specific properties in Mapperly mappers
/// Note: ABP auditing properties are typically read-only on interfaces and managed by the framework
/// These extensions are for scenarios where you need to work with concrete DTOs
/// </summary>
public static class AbpMapperExtensions
{
    /// <summary>
    /// Maps auditing properties from entity to DTO (for concrete DTO types)
    /// </summary>
    public static void MapAuditingProperties<TEntity, TDto>(TEntity entity, TDto dto)
        where TEntity : IHasCreationTime
        where TDto : class
    {
        if (dto is IHasCreationTime creationTimeDto)
        {
            // Use reflection to set the property since interface properties are read-only
            var creationTimeProp = typeof(TDto).GetProperty(nameof(IHasCreationTime.CreationTime));
            creationTimeProp?.SetValue(dto, entity.CreationTime);
        }

        if (entity is IAuditedObject auditedEntity && dto is IAuditedObject auditedDto)
        {
            var creatorIdProp = typeof(TDto).GetProperty(nameof(IAuditedObject.CreatorId));
            var lastModTimeProp = typeof(TDto).GetProperty(nameof(IAuditedObject.LastModificationTime));
            var lastModIdProp = typeof(TDto).GetProperty(nameof(IAuditedObject.LastModifierId));

            creatorIdProp?.SetValue(dto, auditedEntity.CreatorId);
            lastModTimeProp?.SetValue(dto, auditedEntity.LastModificationTime);
            lastModIdProp?.SetValue(dto, auditedEntity.LastModifierId);
        }

        if (entity is IFullAuditedObject fullAuditedEntity && dto is IFullAuditedObject fullAuditedDto)
        {
            var isDeletedProp = typeof(TDto).GetProperty(nameof(IFullAuditedObject.IsDeleted));
            var deleterIdProp = typeof(TDto).GetProperty(nameof(IFullAuditedObject.DeleterId));
            var deletionTimeProp = typeof(TDto).GetProperty(nameof(IFullAuditedObject.DeletionTime));

            isDeletedProp?.SetValue(dto, fullAuditedEntity.IsDeleted);
            deleterIdProp?.SetValue(dto, fullAuditedEntity.DeleterId);
            deletionTimeProp?.SetValue(dto, fullAuditedEntity.DeletionTime);
        }
    }

    /// <summary>
    /// Helper method to calculate years of service for employees
    /// </summary>
    public static int CalculateYearsOfService(DateTime hireDate)
    {
        var today = DateTime.Today;
        var age = today.Year - hireDate.Year;
        if (hireDate.Date > today.AddYears(-age)) age--;
        return Math.Max(0, age);
    }

    /// <summary>
    /// Helper method to format salary as currency
    /// </summary>
    public static string FormatSalary(decimal salary)
    {
        return salary.ToString("C");
    }

    /// <summary>
    /// Helper method to format contact information
    /// </summary>
    public static string FormatContactInfo(string email, string? phoneNumber)
    {
        if (!string.IsNullOrEmpty(phoneNumber))
        {
            return $"{email} | {phoneNumber}";
        }
        return email;
    }
}
