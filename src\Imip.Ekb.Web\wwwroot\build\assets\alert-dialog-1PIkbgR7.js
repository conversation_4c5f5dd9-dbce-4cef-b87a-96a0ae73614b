import{e as b,S as ve,d as p,f as De,h as N,k as w,g as I,q as xe,u as Ae,m as P,P as h,i as A,n as ye,o as Ne,R as he,j as _e,p as Re,F as Ce,D as be,s as Ee,t as S}from"./card-D9JhgxcF.js";import{j as a,r as s}from"./App-Dy45zejD.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Oe=[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]],Nt=b("square-pen",Oe);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Pe=[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]],ht=b("trash-2",Pe);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]],_t=b("user-check",Te);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const je=[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]],Rt=b("user-x",je),Me=De("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Ct({className:e,variant:t,asChild:n=!1,...o}){const r=n?ve:"span";return a.jsx(r,{"data-slot":"badge",className:p(Me({variant:t}),e),...o})}function we(e,t){return s.useReducer((n,o)=>t[n][o]??n,e)}var E=e=>{const{present:t,children:n}=e,o=Ie(t),r=typeof n=="function"?n({present:o.isPresent}):s.Children.only(n),i=N(o.ref,Se(r));return typeof n=="function"||o.isPresent?s.cloneElement(r,{ref:i}):null};E.displayName="Presence";function Ie(e){const[t,n]=s.useState(),o=s.useRef(null),r=s.useRef(e),i=s.useRef("none"),l=e?"mounted":"unmounted",[d,u]=we(l,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return s.useEffect(()=>{const c=R(o.current);i.current=d==="mounted"?c:"none"},[d]),w(()=>{const c=o.current,g=r.current;if(g!==e){const _=i.current,D=R(c);e?u("MOUNT"):D==="none"||(c==null?void 0:c.display)==="none"?u("UNMOUNT"):u(g&&_!==D?"ANIMATION_OUT":"UNMOUNT"),r.current=e}},[e,u]),w(()=>{if(t){let c;const g=t.ownerDocument.defaultView??window,v=D=>{const pe=R(o.current).includes(D.animationName);if(D.target===t&&pe&&(u("ANIMATION_END"),!r.current)){const me=t.style.animationFillMode;t.style.animationFillMode="forwards",c=g.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=me)})}},_=D=>{D.target===t&&(i.current=R(o.current))};return t.addEventListener("animationstart",_),t.addEventListener("animationcancel",v),t.addEventListener("animationend",v),()=>{g.clearTimeout(c),t.removeEventListener("animationstart",_),t.removeEventListener("animationcancel",v),t.removeEventListener("animationend",v)}}else u("ANIMATION_END")},[t,u]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:s.useCallback(c=>{o.current=c?getComputedStyle(c):null,n(c)},[])}}function R(e){return(e==null?void 0:e.animationName)||"none"}function Se(e){var o,r;let t=(o=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(r=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}var O="Dialog",[k,$]=I(O),[ke,f]=k(O),F=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:r,onOpenChange:i,modal:l=!0}=e,d=s.useRef(null),u=s.useRef(null),[c,g]=Ae({prop:o,defaultProp:r??!1,onChange:i,caller:O});return a.jsx(ke,{scope:t,triggerRef:d,contentRef:u,contentId:P(),titleId:P(),descriptionId:P(),open:c,onOpenChange:g,onOpenToggle:s.useCallback(()=>g(v=>!v),[g]),modal:l,children:n})};F.displayName=O;var L="DialogTrigger",W=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=f(L,n),i=N(t,r.triggerRef);return a.jsx(h.button,{type:"button","aria-haspopup":"dialog","aria-expanded":r.open,"aria-controls":r.contentId,"data-state":M(r.open),...o,ref:i,onClick:A(e.onClick,r.onOpenToggle)})});W.displayName=L;var T="DialogPortal",[$e,U]=k(T,{forceMount:void 0}),G=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:r}=e,i=f(T,t);return a.jsx($e,{scope:t,forceMount:n,children:s.Children.map(o,l=>a.jsx(E,{present:n||i.open,children:a.jsx(ye,{asChild:!0,container:r,children:l})}))})};G.displayName=T;var C="DialogOverlay",V=s.forwardRef((e,t)=>{const n=U(C,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=f(C,e.__scopeDialog);return i.modal?a.jsx(E,{present:o||i.open,children:a.jsx(Le,{...r,ref:t})}):null});V.displayName=C;var Fe=_e("DialogOverlay.RemoveScroll"),Le=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=f(C,n);return a.jsx(he,{as:Fe,allowPinchZoom:!0,shards:[r.contentRef],children:a.jsx(h.div,{"data-state":M(r.open),...o,ref:t,style:{pointerEvents:"auto",...o.style}})})}),x="DialogContent",q=s.forwardRef((e,t)=>{const n=U(x,e.__scopeDialog),{forceMount:o=n.forceMount,...r}=e,i=f(x,e.__scopeDialog);return a.jsx(E,{present:o||i.open,children:i.modal?a.jsx(We,{...r,ref:t}):a.jsx(Ue,{...r,ref:t})})});q.displayName=x;var We=s.forwardRef((e,t)=>{const n=f(x,e.__scopeDialog),o=s.useRef(null),r=N(t,n.contentRef,o);return s.useEffect(()=>{const i=o.current;if(i)return Ne(i)},[]),a.jsx(z,{...e,ref:r,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:A(e.onCloseAutoFocus,i=>{var l;i.preventDefault(),(l=n.triggerRef.current)==null||l.focus()}),onPointerDownOutside:A(e.onPointerDownOutside,i=>{const l=i.detail.originalEvent,d=l.button===0&&l.ctrlKey===!0;(l.button===2||d)&&i.preventDefault()}),onFocusOutside:A(e.onFocusOutside,i=>i.preventDefault())})}),Ue=s.forwardRef((e,t)=>{const n=f(x,e.__scopeDialog),o=s.useRef(!1),r=s.useRef(!1);return a.jsx(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:i=>{var l,d;(l=e.onCloseAutoFocus)==null||l.call(e,i),i.defaultPrevented||(o.current||(d=n.triggerRef.current)==null||d.focus(),i.preventDefault()),o.current=!1,r.current=!1},onInteractOutside:i=>{var u,c;(u=e.onInteractOutside)==null||u.call(e,i),i.defaultPrevented||(o.current=!0,i.detail.originalEvent.type==="pointerdown"&&(r.current=!0));const l=i.target;((c=n.triggerRef.current)==null?void 0:c.contains(l))&&i.preventDefault(),i.detail.originalEvent.type==="focusin"&&r.current&&i.preventDefault()}})}),z=s.forwardRef((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:r,onCloseAutoFocus:i,...l}=e,d=f(x,n),u=s.useRef(null),c=N(t,u);return Re(),a.jsxs(a.Fragment,{children:[a.jsx(Ce,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:r,onUnmountAutoFocus:i,children:a.jsx(be,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":M(d.open),...l,ref:c,onDismiss:()=>d.onOpenChange(!1)})}),a.jsxs(a.Fragment,{children:[a.jsx(Ve,{titleId:d.titleId}),a.jsx(ze,{contentRef:u,descriptionId:d.descriptionId})]})]})}),j="DialogTitle",H=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=f(j,n);return a.jsx(h.h2,{id:r.titleId,...o,ref:t})});H.displayName=j;var B="DialogDescription",Y=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=f(B,n);return a.jsx(h.p,{id:r.descriptionId,...o,ref:t})});Y.displayName=B;var K="DialogClose",X=s.forwardRef((e,t)=>{const{__scopeDialog:n,...o}=e,r=f(K,n);return a.jsx(h.button,{type:"button",...o,ref:t,onClick:A(e.onClick,()=>r.onOpenChange(!1))})});X.displayName=K;function M(e){return e?"open":"closed"}var Z="DialogTitleWarning",[Ge,J]=xe(Z,{contentName:x,titleName:j,docsSlug:"dialog"}),Ve=({titleId:e})=>{const t=J(Z),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return s.useEffect(()=>{e&&(document.getElementById(e)||console.error(n))},[n,e]),null},qe="DialogDescriptionWarning",ze=({contentRef:e,descriptionId:t})=>{const o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${J(qe).contentName}}.`;return s.useEffect(()=>{var i;const r=(i=e.current)==null?void 0:i.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},He=F,Be=W,Ye=G,Ke=V,Xe=q,Ze=H,Je=Y,Q=X,ee="AlertDialog",[Qe,bt]=I(ee,[$]),m=$(),te=e=>{const{__scopeAlertDialog:t,...n}=e,o=m(t);return a.jsx(He,{...o,...n,modal:!0})};te.displayName=ee;var et="AlertDialogTrigger",oe=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=m(n);return a.jsx(Be,{...r,...o,ref:t})});oe.displayName=et;var tt="AlertDialogPortal",ne=e=>{const{__scopeAlertDialog:t,...n}=e,o=m(t);return a.jsx(Ye,{...o,...n})};ne.displayName=tt;var ot="AlertDialogOverlay",re=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=m(n);return a.jsx(Ke,{...r,...o,ref:t})});re.displayName=ot;var y="AlertDialogContent",[nt,rt]=Qe(y),at=Ee("AlertDialogContent"),ae=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,children:o,...r}=e,i=m(n),l=s.useRef(null),d=N(t,l),u=s.useRef(null);return a.jsx(Ge,{contentName:y,titleName:ie,docsSlug:"alert-dialog",children:a.jsx(nt,{scope:n,cancelRef:u,children:a.jsxs(Xe,{role:"alertdialog",...i,...r,ref:d,onOpenAutoFocus:A(r.onOpenAutoFocus,c=>{var g;c.preventDefault(),(g=u.current)==null||g.focus({preventScroll:!0})}),onPointerDownOutside:c=>c.preventDefault(),onInteractOutside:c=>c.preventDefault(),children:[a.jsx(at,{children:o}),a.jsx(st,{contentRef:l})]})})})});ae.displayName=y;var ie="AlertDialogTitle",se=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=m(n);return a.jsx(Ze,{...r,...o,ref:t})});se.displayName=ie;var le="AlertDialogDescription",ce=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=m(n);return a.jsx(Je,{...r,...o,ref:t})});ce.displayName=le;var it="AlertDialogAction",de=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,r=m(n);return a.jsx(Q,{...r,...o,ref:t})});de.displayName=it;var ue="AlertDialogCancel",ge=s.forwardRef((e,t)=>{const{__scopeAlertDialog:n,...o}=e,{cancelRef:r}=rt(ue,n),i=m(n),l=N(t,r);return a.jsx(Q,{...i,...o,ref:l})});ge.displayName=ue;var st=({contentRef:e})=>{const t=`\`${y}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${y}\` by passing a \`${le}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${y}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return s.useEffect(()=>{var o;document.getElementById((o=e.current)==null?void 0:o.getAttribute("aria-describedby"))||console.warn(t)},[t,e]),null},lt=te,ct=oe,dt=ne,ut=re,gt=ae,ft=de,pt=ge,mt=se,vt=ce;function Et({...e}){return a.jsx(lt,{"data-slot":"alert-dialog",...e})}function Ot({...e}){return a.jsx(ct,{"data-slot":"alert-dialog-trigger",...e})}function Dt({...e}){return a.jsx(dt,{"data-slot":"alert-dialog-portal",...e})}function xt({className:e,...t}){return a.jsx(ut,{"data-slot":"alert-dialog-overlay",className:p("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",e),...t})}function Pt({className:e,...t}){return a.jsxs(Dt,{children:[a.jsx(xt,{}),a.jsx(gt,{"data-slot":"alert-dialog-content",className:p("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",e),...t})]})}function Tt({className:e,...t}){return a.jsx("div",{"data-slot":"alert-dialog-header",className:p("flex flex-col gap-2 text-center sm:text-left",e),...t})}function jt({className:e,...t}){return a.jsx("div",{"data-slot":"alert-dialog-footer",className:p("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",e),...t})}function Mt({className:e,...t}){return a.jsx(mt,{"data-slot":"alert-dialog-title",className:p("text-lg font-semibold",e),...t})}function wt({className:e,...t}){return a.jsx(vt,{"data-slot":"alert-dialog-description",className:p("text-muted-foreground text-sm",e),...t})}function It({className:e,...t}){return a.jsx(ft,{className:p(S(),e),...t})}function St({className:e,...t}){return a.jsx(pt,{className:p(S({variant:"outline"}),e),...t})}export{Et as A,Ct as B,E as P,Nt as S,ht as T,Rt as U,Ot as a,Pt as b,Tt as c,Mt as d,wt as e,jt as f,St as g,It as h,_t as i};
