using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Attachments;

/// <summary>
/// Input DTO for getting attachments with paging and optional filtering
/// </summary>
public class GetAttachmentsInput : PagedAndSortedResultRequestDto
{
    /// <summary>
    /// Optional reference ID to filter attachments
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type to filter attachments
    /// </summary>
    public string? ReferenceType { get; set; }

    /// <summary>
    /// Optional file name filter (contains search)
    /// </summary>
    public string? FileNameFilter { get; set; }
}
