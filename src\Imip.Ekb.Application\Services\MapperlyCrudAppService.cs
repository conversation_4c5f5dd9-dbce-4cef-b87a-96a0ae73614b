using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Services;

/// <summary>
/// Base CRUD Application Service that uses Mapperly for mapping operations
/// Eliminates the need to override mapping methods in each service
/// </summary>
/// <typeparam name="TEntity">The entity type</typeparam>
/// <typeparam name="TEntityDto">The entity DTO type</typeparam>
/// <typeparam name="TKey">The primary key type</typeparam>
/// <typeparam name="TGetListInput">The input type for GetList operation</typeparam>
/// <typeparam name="TCreateInput">The input type for Create operation</typeparam>
/// <typeparam name="TUpdateInput">The input type for Update operation</typeparam>
/// <typeparam name="TMapper">The Mapperly mapper type</typeparam>
public abstract class MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper> :
    CrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateInput : class
    where TUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
{
    protected readonly TMapper Mapper;

    protected MapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository)
    {
        Mapper = mapper;
    }

    #region Mapperly Override Methods

    /// <summary>
    /// Override MapToGetOutputDto to use Mapperly instead of AutoMapper
    /// </summary>
    protected override TEntityDto MapToGetOutputDto(TEntity entity)
    {
        return Mapper.ToDto(entity);
    }

    /// <summary>
    /// Override MapToGetListOutputDto to use Mapperly instead of AutoMapper
    /// </summary>
    protected override TEntityDto MapToGetListOutputDto(TEntity entity)
    {
        return Mapper.ToDto(entity);
    }

    /// <summary>
    /// Override MapToEntity for Create operations to use Mapperly instead of AutoMapper
    /// </summary>
    protected override TEntity MapToEntity(TCreateInput createInput)
    {
        return Mapper.ToEntity(createInput);
    }

    /// <summary>
    /// Override MapToEntity for Update operations to use Mapperly instead of AutoMapper
    /// </summary>
    protected override void MapToEntity(TUpdateInput updateInput, TEntity entity)
    {
        Mapper.UpdateEntity(updateInput, entity);
    }

    /// <summary>
    /// Override MapToGetListOutputDtosAsync to use Mapperly for batch mapping
    /// </summary>
    protected override Task<List<TEntityDto>> MapToGetListOutputDtosAsync(List<TEntity> entities)
    {
        return Task.FromResult(Mapper.ToDto(entities));
    }

    #endregion

    #region Helper Methods for Derived Classes

    /// <summary>
    /// Helper method for derived classes to access the mapper
    /// Useful for custom mapping scenarios
    /// </summary>
    protected TMapper GetMapper() => Mapper;

    /// <summary>
    /// Helper method to map a single entity to DTO
    /// Can be used in custom methods
    /// </summary>
    protected TEntityDto MapEntityToDto(TEntity entity)
    {
        return Mapper.ToDto(entity);
    }

    /// <summary>
    /// Helper method to map a list of entities to DTOs
    /// Can be used in custom methods
    /// </summary>
    protected List<TEntityDto> MapEntitiesToDtos(List<TEntity> entities)
    {
        return Mapper.ToDto(entities);
    }

    #endregion
}

/// <summary>
/// Simplified base class for cases where Create and Update DTOs are the same
/// </summary>
/// <typeparam name="TEntity">The entity type</typeparam>
/// <typeparam name="TEntityDto">The entity DTO type</typeparam>
/// <typeparam name="TKey">The primary key type</typeparam>
/// <typeparam name="TGetListInput">The input type for GetList operation</typeparam>
/// <typeparam name="TCreateUpdateInput">The input type for both Create and Update operations</typeparam>
/// <typeparam name="TMapper">The Mapperly mapper type</typeparam>
public abstract class MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TMapper> :
    MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TCreateUpdateInput, TMapper>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateUpdateInput, TCreateUpdateInput>
{
    protected MapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository, mapper)
    {
    }
}
