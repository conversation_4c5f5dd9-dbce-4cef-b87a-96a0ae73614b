using System;
using System.Collections.Generic;
using System.Linq;
using Imip.Ekb.Products;
using Riok.Mapperly.Abstractions;

namespace Imip.Ekb.Mapping;

/// <summary>
/// Mapperly mapper for Product entity and related DTOs
/// Demonstrates simple entity-to-DTO mappings with ABP auditing properties
/// </summary>
[Mapper]
public partial class ProductMapper : IAbpMapper<Product, ProductDto, Guid, CreateUpdateProductDto>
{
    /// <summary>
    /// Maps Product entity to ProductDto
    /// Automatically maps all matching properties including ABP auditing properties
    /// </summary>
    public partial ProductDto ToDto(Product entity);

    /// <summary>
    /// Maps list of Product entities to list of ProductDto
    /// </summary>
    public partial List<ProductDto> ToDto(List<Product> entities);

    /// <summary>
    /// Creates a new Product entity from CreateUpdateProductDto
    /// Uses the entity constructor to properly initialize the entity
    /// </summary>
    public Product ToEntity(CreateUpdateProductDto createDto)
    {
        return new Product(
            Guid.NewGuid(),
            createDto.Name,
            createDto.Description,
            createDto.Price,
            createDto.StockQuantity,
            createDto.SKU,
            createDto.IsActive
        );
    }

    /// <summary>
    /// Creates a new Product entity from CreateUpdateProductDto with specified ID
    /// Used for creating new entities with predetermined ID
    /// </summary>
    public Product ToEntity(CreateUpdateProductDto createDto, Guid id)
    {
        return new Product(
            id,
            createDto.Name,
            createDto.Description,
            createDto.Price,
            createDto.StockQuantity,
            createDto.SKU,
            createDto.IsActive
        );
    }

    /// <summary>
    /// Updates existing Product entity with data from CreateUpdateProductDto
    /// Only maps business properties, preserves auditing and system properties
    /// </summary>
    public void UpdateEntity(CreateUpdateProductDto updateDto, Product entity)
    {
        entity.Name = updateDto.Name;
        entity.Description = updateDto.Description;
        entity.Price = updateDto.Price;
        entity.StockQuantity = updateDto.StockQuantity;
        entity.SKU = updateDto.SKU;
        entity.IsActive = updateDto.IsActive;
    }

    /// <summary>
    /// Custom mapping method for complex scenarios
    /// Demonstrates how to handle custom business logic during mapping
    /// </summary>
    public ProductDto ToDtoWithCustomLogic(Product entity)
    {
        var dto = ToDto(entity);

        // Example: Add custom business logic
        // dto.DisplayName = $"{entity.Name} ({entity.SKU})";

        return dto;
    }

    /// <summary>
    /// Maps Product entity to a simplified DTO for list views
    /// Demonstrates selective property mapping
    /// </summary>
    public ProductListItemDto ToListItemDto(Product entity)
    {
        return new ProductListItemDto
        {
            Id = entity.Id,
            Name = entity.Name,
            SKU = entity.SKU,
            Price = entity.Price,
            IsActive = entity.IsActive
        };
    }

    /// <summary>
    /// Maps list of Product entities to simplified list DTOs
    /// </summary>
    public List<ProductListItemDto> ToListItemDto(List<Product> entities)
    {
        return entities.Select(ToListItemDto).ToList();
    }
}

/// <summary>
/// Simplified DTO for product list views
/// Demonstrates how to create lightweight DTOs for performance-critical scenarios
/// </summary>
public class ProductListItemDto
{
    public Guid Id { get; set; }
    public string Name { get; set; } = string.Empty;
    public string SKU { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public bool IsActive { get; set; }
}
