﻿using Imip.Ekb.Mapping;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Account;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Modularity;
using Volo.Abp.PermissionManagement;
using Volo.Abp.SettingManagement;
using Volo.Abp.TenantManagement;

namespace Imip.Ekb;

[DependsOn(
    typeof(EkbDomainModule),
    typeof(EkbApplicationContractsModule),
    typeof(AbpPermissionManagementApplicationModule),
    typeof(AbpFeatureManagementApplicationModule),
    typeof(AbpIdentityApplicationModule),
    typeof(AbpAccountApplicationModule),
    typeof(AbpTenantManagementApplicationModule),
    typeof(AbpSettingManagementApplicationModule)
    )]
public class EkbApplicationModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // Configure AutoMapper (existing functionality)
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<EkbApplicationModule>();
        });

        // Configure Mapperly mappers (new functionality)
        ConfigureMapperlyMappers(context);
    }

    private void ConfigureMapperlyMappers(ServiceConfigurationContext context)
    {
        // Register Mapperly mappers in DI container
        // Using singleton registration for better performance since mappers are stateless
        context.Services.AddSingleton<ProductMapper>();
        context.Services.AddSingleton<EmployeeMapper>();

        // Register as interfaces for dependency injection
        context.Services.AddSingleton<IAbpMapper<Products.Product, Products.ProductDto, System.Guid, Products.CreateUpdateProductDto>>(
            provider => provider.GetRequiredService<ProductMapper>());

        context.Services.AddSingleton<IAbpMapper<Employees.Employee, Employees.EmployeeDto, System.Guid, Employees.CreateUpdateEmployeeDto>>(
            provider => provider.GetRequiredService<EmployeeMapper>());
    }
}
