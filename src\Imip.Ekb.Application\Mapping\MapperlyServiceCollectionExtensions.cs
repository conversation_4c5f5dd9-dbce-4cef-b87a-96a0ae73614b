using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Modularity;

namespace Imip.Ekb.Mapping;

/// <summary>
/// Extension methods for registering Mapperly mappers in ABP's dependency injection container
/// </summary>
public static class MapperlyServiceCollectionExtensions
{
    /// <summary>
    /// Registers all Mapperly mappers as singletons in the DI container
    /// Mappers are stateless and can be safely registered as singletons for better performance
    /// </summary>
    public static IServiceCollection AddMapperlyMappers(this IServiceCollection services)
    {
        // Register specific mappers
        services.AddSingleton<ProductMapper>();
        services.AddSingleton<EmployeeMapper>();

        // Register as interfaces for dependency injection
        services.AddSingleton<IAbpMapper<Products.Product, Products.ProductDto, System.Guid, Products.CreateUpdateProductDto>>(
            provider => provider.GetRequiredService<ProductMapper>());

        services.AddSingleton<IAbpMapper<Employees.Employee, Employees.EmployeeDto, System.Guid, Employees.CreateUpdateEmployeeDto>>(
            provider => provider.GetRequiredService<EmployeeMapper>());

        return services;
    }

    /// <summary>
    /// Alternative registration method using ABP's conventional registration
    /// This method automatically registers all mappers that implement IAbpMapper interfaces
    /// </summary>
    public static void ConfigureMapperlyMappers(ServiceConfigurationContext context)
    {
        // Register mappers as transient services (ABP default)
        // Note: Consider changing to singleton for better performance if mappers are stateless
        context.Services.AddTransient<ProductMapper>();
        context.Services.AddTransient<EmployeeMapper>();

        // Register interface implementations
        context.Services.AddTransient<IAbpMapper<Products.Product, Products.ProductDto, System.Guid, Products.CreateUpdateProductDto>, ProductMapper>();
        context.Services.AddTransient<IAbpMapper<Employees.Employee, Employees.EmployeeDto, System.Guid, Employees.CreateUpdateEmployeeDto>, EmployeeMapper>();
    }
}

/// <summary>
/// ABP service for automatic mapper discovery and registration
/// This approach follows ABP conventions for service registration
/// </summary>
public class MapperlyMapperRegistrar : ITransientDependency
{
    /// <summary>
    /// Registers mappers using ABP's service registration conventions
    /// This method can be called during module configuration
    /// </summary>
    public static void RegisterMappers(IServiceCollection services)
    {
        // Register concrete mapper classes
        services.AddTransient<ProductMapper>();
        services.AddTransient<EmployeeMapper>();

        // You can add more mappers here as your application grows
        // services.AddTransient<OrderMapper>();
        // services.AddTransient<CustomerMapper>();
    }
}
