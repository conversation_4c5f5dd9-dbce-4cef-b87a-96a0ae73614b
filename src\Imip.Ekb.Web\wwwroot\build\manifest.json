{"_alert-DbmVDqVT.js": {"file": "assets/alert-DbmVDqVT.js", "name": "alert", "imports": ["src/App.tsx", "_card-D9JhgxcF.js"]}, "_alert-dialog-1PIkbgR7.js": {"file": "assets/alert-dialog-1PIkbgR7.js", "name": "alert-dialog", "imports": ["_card-D9JhgxcF.js", "src/App.tsx"]}, "_arrow-left-D2mpE_8W.js": {"file": "assets/arrow-left-D2mpE_8W.js", "name": "arrow-left", "imports": ["_card-D9JhgxcF.js"]}, "_card-D9JhgxcF.js": {"file": "assets/card-D9JhgxcF.js", "name": "card", "imports": ["src/App.tsx"]}, "_select-ZRFupiEN.js": {"file": "assets/select-ZRFupiEN.js", "name": "select", "imports": ["src/App.tsx", "_card-D9JhgxcF.js"]}, "src/App.tsx": {"file": "assets/App-Dy45zejD.js", "name": "App", "src": "src/App.tsx", "isEntry": true, "dynamicImports": ["src/Pages/Employees/Create.tsx", "src/Pages/Employees/Edit.tsx", "src/Pages/Employees/Index.tsx", "src/Pages/Employees/Show.tsx", "src/Pages/Home.tsx"], "css": ["assets/App-CHdW1L7K.css"]}, "src/Pages/Employees/Create.tsx": {"file": "assets/Create-DBKghMTR.js", "name": "Create", "src": "src/Pages/Employees/Create.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-D9JhgxcF.js", "_select-ZRFupiEN.js", "_alert-DbmVDqVT.js", "_arrow-left-D2mpE_8W.js"]}, "src/Pages/Employees/Edit.tsx": {"file": "assets/Edit-BMzdUfaP.js", "name": "Edit", "src": "src/Pages/Employees/Edit.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-D9JhgxcF.js", "_select-ZRFupiEN.js", "_alert-DbmVDqVT.js", "_arrow-left-D2mpE_8W.js"]}, "src/Pages/Employees/Index.tsx": {"file": "assets/Index-CoO1zljN.js", "name": "Index", "src": "src/Pages/Employees/Index.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-D9JhgxcF.js", "_select-ZRFupiEN.js", "_alert-dialog-1PIkbgR7.js"]}, "src/Pages/Employees/Show.tsx": {"file": "assets/Show-gjutCZIC.js", "name": "Show", "src": "src/Pages/Employees/Show.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx", "_card-D9JhgxcF.js", "_alert-dialog-1PIkbgR7.js", "_arrow-left-D2mpE_8W.js"]}, "src/Pages/Home.tsx": {"file": "assets/Home-Cfhv9JpA.js", "name": "Home", "src": "src/Pages/Home.tsx", "isDynamicEntry": true, "imports": ["src/App.tsx"]}}