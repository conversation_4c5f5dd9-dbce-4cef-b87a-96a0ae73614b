using System;
using Volo.Abp.Application.Dtos;

namespace Imip.Ekb.Employees;

public class EmployeeDto : FullAuditedEntityDto<Guid>
{
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Department { get; set; } = string.Empty;
    public string? Position { get; set; }
    public DateTime HireDate { get; set; }
    public decimal Salary { get; set; }
    public string? PhotoPath { get; set; }
    public bool IsActive { get; set; }
}