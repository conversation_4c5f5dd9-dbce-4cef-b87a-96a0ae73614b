import{a as H,r as v,j as e,Y as _,m as y}from"./App-Dy45zejD.js";import{e as g,g as Y,P as C,l as V,k as E,d as L,B as j,C as S,a as A,b as w,c as b}from"./card-D9JhgxcF.js";import{U as G,i as O,S as K,A as W,a as X,T as J,b as Q,c as Z,d as ee,e as te,f as ae,g as se,h as re,B as I}from"./alert-dialog-1PIkbgR7.js";import{A as ne}from"./arrow-left-D2mpE_8W.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ie=[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]],ce=g("building",ie);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const le=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],de=g("calendar",le);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const oe=[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]],he=g("dollar-sign",oe);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ue=[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]],xe=g("mail",ue);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const me=[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]],fe=g("phone",me);var k={exports:{}},D={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var $;function ve(){if($)return D;$=1;var t=H();function a(r,n){return r===n&&(r!==0||1/r===1/n)||r!==r&&n!==n}var l=typeof Object.is=="function"?Object.is:a,i=t.useState,d=t.useEffect,s=t.useLayoutEffect,u=t.useDebugValue;function o(r,n){var m=n(),p=i({inst:{value:m,getSnapshot:n}}),f=p[0].inst,N=p[1];return s(function(){f.value=m,f.getSnapshot=n,h(f)&&N({inst:f})},[r,m,n]),d(function(){return h(f)&&N({inst:f}),r(function(){h(f)&&N({inst:f})})},[r]),u(m),m}function h(r){var n=r.getSnapshot;r=r.value;try{var m=n();return!l(r,m)}catch{return!0}}function c(r,n){return n()}var x=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?c:o;return D.useSyncExternalStore=t.useSyncExternalStore!==void 0?t.useSyncExternalStore:x,D}var R;function ge(){return R||(R=1,k.exports=ve()),k.exports}var je=ge();function pe(){return je.useSyncExternalStore(Ne,()=>!0,()=>!1)}function Ne(){return()=>{}}var M="Avatar",[ye,$e]=Y(M),[Se,q]=ye(M),F=v.forwardRef((t,a)=>{const{__scopeAvatar:l,...i}=t,[d,s]=v.useState("idle");return e.jsx(Se,{scope:l,imageLoadingStatus:d,onImageLoadingStatusChange:s,children:e.jsx(C.span,{...i,ref:a})})});F.displayName=M;var U="AvatarImage",P=v.forwardRef((t,a)=>{const{__scopeAvatar:l,src:i,onLoadingStatusChange:d=()=>{},...s}=t,u=q(U,l),o=Ae(i,s),h=V(c=>{d(c),u.onImageLoadingStatusChange(c)});return E(()=>{o!=="idle"&&h(o)},[o,h]),o==="loaded"?e.jsx(C.img,{...s,ref:a,src:i}):null});P.displayName=U;var z="AvatarFallback",B=v.forwardRef((t,a)=>{const{__scopeAvatar:l,delayMs:i,...d}=t,s=q(z,l),[u,o]=v.useState(i===void 0);return v.useEffect(()=>{if(i!==void 0){const h=window.setTimeout(()=>o(!0),i);return()=>window.clearTimeout(h)}},[i]),u&&s.imageLoadingStatus!=="loaded"?e.jsx(C.span,{...d,ref:a}):null});B.displayName=z;function T(t,a){return t?a?(t.src!==a&&(t.src=a),t.complete&&t.naturalWidth>0?"loaded":"loading"):"error":"idle"}function Ae(t,{referrerPolicy:a,crossOrigin:l}){const i=pe(),d=v.useRef(null),s=i?(d.current||(d.current=new window.Image),d.current):null,[u,o]=v.useState(()=>T(s,t));return E(()=>{o(T(s,t))},[s,t]),E(()=>{const h=r=>()=>{o(r)};if(!s)return;const c=h("loaded"),x=h("error");return s.addEventListener("load",c),s.addEventListener("error",x),a&&(s.referrerPolicy=a),typeof l=="string"&&(s.crossOrigin=l),()=>{s.removeEventListener("load",c),s.removeEventListener("error",x)}},[s,l,a]),u}var we=F,be=P,ke=B;function De({className:t,...a}){return e.jsx(we,{"data-slot":"avatar",className:L("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function Ee({className:t,...a}){return e.jsx(be,{"data-slot":"avatar-image",className:L("aspect-square size-full",t),...a})}function Ce({className:t,...a}){return e.jsx(ke,{"data-slot":"avatar-fallback",className:L("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}const Re=({employee:t,permissions:a})=>{const l=()=>{y.post(`/employees/${t.id}/delete`)},i=()=>{y.post(`/employees/${t.id}/activate`)},d=()=>{y.post(`/employees/${t.id}/deactivate`)},s=c=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(c),u=c=>new Date(c).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}),o=(c,x)=>`${c.charAt(0)}${x.charAt(0)}`.toUpperCase(),h=c=>{const x=new Date(c),r=new Date,n=r.getFullYear()-x.getFullYear(),m=r.getMonth()-x.getMonth();return m<0||m===0&&r.getDate()<x.getDate()?n-1:n};return e.jsxs("div",{className:"container mx-auto p-6 max-w-4xl",children:[e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[e.jsxs("div",{className:"flex items-center gap-4",children:[e.jsx(_,{href:"/employees",children:e.jsxs(j,{variant:"outline",size:"sm",children:[e.jsx(ne,{className:"w-4 h-4 mr-2"}),"Back to Employees"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:t.fullName}),e.jsxs("p",{className:"text-gray-600 mt-1",children:[t.position," • ",t.department]})]})]}),e.jsxs("div",{className:"flex gap-2",children:[a.manageActivation&&e.jsx(j,{variant:"outline",onClick:t.isActive?d:i,children:t.isActive?e.jsxs(e.Fragment,{children:[e.jsx(G,{className:"w-4 h-4 mr-2"}),"Deactivate"]}):e.jsxs(e.Fragment,{children:[e.jsx(O,{className:"w-4 h-4 mr-2"}),"Activate"]})}),a.edit&&e.jsx(_,{href:`/employees/${t.id}/edit`,children:e.jsxs(j,{variant:"outline",children:[e.jsx(K,{className:"w-4 h-4 mr-2"}),"Edit"]})}),a.delete&&e.jsxs(W,{children:[e.jsx(X,{asChild:!0,children:e.jsxs(j,{variant:"destructive",children:[e.jsx(J,{className:"w-4 h-4 mr-2"}),"Delete"]})}),e.jsxs(Q,{children:[e.jsxs(Z,{children:[e.jsx(ee,{children:"Delete Employee"}),e.jsxs(te,{children:["Are you sure you want to delete ",t.fullName,"? This action cannot be undone."]})]}),e.jsxs(ae,{children:[e.jsx(se,{children:"Cancel"}),e.jsx(re,{onClick:l,children:"Delete"})]})]})]})]})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[e.jsxs(S,{className:"lg:col-span-1",children:[e.jsxs(A,{className:"text-center",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsxs(De,{className:"w-24 h-24",children:[e.jsx(Ee,{src:t.photoPath,alt:t.fullName}),e.jsx(Ce,{className:"text-lg",children:o(t.firstName,t.lastName)})]})}),e.jsx(w,{className:"text-xl",children:t.fullName}),e.jsx("div",{className:"flex justify-center",children:e.jsx(I,{variant:t.isActive?"default":"secondary",children:t.isActive?"Active":"Inactive"})})]}),e.jsxs(b,{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center gap-3 text-sm",children:[e.jsx(xe,{className:"w-4 h-4 text-gray-500"}),e.jsx("a",{href:`mailto:${t.email}`,className:"text-blue-600 hover:underline",children:t.email})]}),t.phoneNumber&&e.jsxs("div",{className:"flex items-center gap-3 text-sm",children:[e.jsx(fe,{className:"w-4 h-4 text-gray-500"}),e.jsx("a",{href:`tel:${t.phoneNumber}`,className:"text-blue-600 hover:underline",children:t.phoneNumber})]}),e.jsxs("div",{className:"flex items-center gap-3 text-sm",children:[e.jsx(ce,{className:"w-4 h-4 text-gray-500"}),e.jsx("span",{children:t.department})]})]})]}),e.jsxs(S,{className:"lg:col-span-2",children:[e.jsx(A,{children:e.jsx(w,{children:"Employee Details"})}),e.jsx(b,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Position"}),e.jsx("p",{className:"text-lg font-semibold",children:t.position})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Department"}),e.jsx("p",{className:"text-lg",children:t.department})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Employee ID"}),e.jsx("p",{className:"text-sm font-mono text-gray-600",children:t.id})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Salary"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(he,{className:"w-4 h-4 text-green-600"}),e.jsx("p",{className:"text-lg font-semibold text-green-600",children:s(t.salary)})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Hire Date"}),e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx(de,{className:"w-4 h-4 text-gray-500"}),e.jsx("p",{className:"text-lg",children:u(t.hireDate)})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-500",children:"Years of Service"}),e.jsxs("p",{className:"text-lg font-semibold",children:[h(t.hireDate)," years"]})]})]})]})})]}),e.jsxs(S,{className:"lg:col-span-3",children:[e.jsx(A,{children:e.jsx(w,{children:"System Information"})}),e.jsx(b,{children:e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 text-sm",children:[e.jsxs("div",{children:[e.jsx("label",{className:"font-medium text-gray-500",children:"Created"}),e.jsx("p",{children:u(t.creationTime)})]}),t.lastModificationTime&&e.jsxs("div",{children:[e.jsx("label",{className:"font-medium text-gray-500",children:"Last Modified"}),e.jsx("p",{children:u(t.lastModificationTime)})]}),e.jsxs("div",{children:[e.jsx("label",{className:"font-medium text-gray-500",children:"Status"}),e.jsx("div",{className:"flex items-center gap-2 mt-1",children:e.jsx(I,{variant:t.isActive?"default":"secondary",children:t.isActive?"Active":"Inactive"})})]})]})})]})]})]})};export{Re as default};
