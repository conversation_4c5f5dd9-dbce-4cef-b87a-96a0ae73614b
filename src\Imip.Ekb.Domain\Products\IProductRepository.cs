using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Products;

/// <summary>
/// Custom repository interface for Product entity with context-specific include methods
/// Follows ABP Framework's repository pattern with EF Core optimizations
/// </summary>
public interface IProductRepository : IRepository<Product, Guid>
{
    /// <summary>
    /// Gets a product with all related data for detail view
    /// Includes: Category, ProductImages, ProductAttributes, Reviews with Customer
    /// Use this for product detail pages where full information is needed
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Product with full details or null if not found</returns>
    Task<Product?> GetWithDetailsAsync(Guid id);

    /// <summary>
    /// Gets a product with minimal related data for edit operations
    /// Includes: Category, ProductAttributes (without Reviews for performance)
    /// Use this for edit forms where you need editable data but not display data
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <returns>Product with edit-relevant data or null if not found</returns>
    Task<Product?> GetForEditAsync(Guid id);

    /// <summary>
    /// Gets products with basic includes optimized for list views
    /// Includes: Category, First ProductImage only
    /// Use this for product listing pages where minimal data is sufficient
    /// </summary>
    /// <returns>List of products with basic information</returns>
    Task<List<Product>> GetListWithBasicIncludesAsync();

    /// <summary>
    /// Gets products by category with optimized includes
    /// Includes: Category, First ProductImage
    /// </summary>
    /// <param name="categoryId">Category ID to filter by</param>
    /// <returns>List of products in the specified category</returns>
    Task<List<Product>> GetByCategoryAsync(Guid categoryId);

    /// <summary>
    /// Gets products with their reviews for a specific date range
    /// Includes: Category, ProductImages, Reviews with Customer (filtered by date)
    /// Use this for analytics or recent activity reports
    /// </summary>
    /// <param name="fromDate">Start date for review filtering</param>
    /// <returns>List of products with recent reviews</returns>
    Task<List<Product>> GetWithRecentReviewsAsync(DateTime fromDate);

    /// <summary>
    /// Gets products with stock information
    /// Includes: Category, ProductImages, Stock information
    /// Use this for inventory management scenarios
    /// </summary>
    /// <returns>List of products with stock data</returns>
    Task<List<Product>> GetWithStockInfoAsync();

    /// <summary>
    /// Search products with includes based on search criteria
    /// Includes: Category, First ProductImage
    /// Supports searching by name, description, and category name
    /// </summary>
    /// <param name="searchTerm">Search term to match against name, description, or category</param>
    /// <param name="categoryId">Optional category filter</param>
    /// <returns>List of products matching search criteria</returns>
    Task<List<Product>> SearchProductsAsync(string searchTerm, Guid? categoryId = null);

    /// <summary>
    /// Gets products with pagination and includes for high-performance list scenarios
    /// Includes: Category, First ProductImage
    /// Use this when you need server-side paging with includes
    /// </summary>
    /// <param name="skipCount">Number of records to skip</param>
    /// <param name="maxResultCount">Maximum number of records to return</param>
    /// <param name="sorting">Sorting expression</param>
    /// <returns>Paged list of products</returns>
    Task<List<Product>> GetPagedListWithIncludesAsync(int skipCount, int maxResultCount, string sorting = null);

    /// <summary>
    /// Gets count of products matching search criteria (for pagination)
    /// </summary>
    /// <param name="searchTerm">Search term</param>
    /// <param name="categoryId">Optional category filter</param>
    /// <returns>Total count of matching products</returns>
    Task<int> GetSearchCountAsync(string searchTerm, Guid? categoryId = null);
}
