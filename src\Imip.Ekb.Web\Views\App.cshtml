@using InertiaCore
@using InertiaCore.Utils
@using Microsoft.AspNetCore.Antiforgery
@using Microsoft.Extensions.DependencyInjection

@{
    Layout = null;
}

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Imip.Ekb Dashboard</title>
    @await Inertia.Head(Model)
    @Vite.Input("src/App.tsx")
</head>

<body>
    @Vite.ReactRefresh()
    @await Inertia.Html(Model)
    @{
        var antiforgery = Context.RequestServices.GetRequiredService<IAntiforgery>();
        var tokens = antiforgery.GetAndStoreTokens(Context);
        var requestToken = tokens.RequestToken;
    }
    <script>
        window.__RequestVerificationToken = '@Html.Raw(requestToken)';
    </script>
</body>

</html>
