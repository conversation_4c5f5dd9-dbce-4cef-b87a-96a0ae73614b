# Complete Product Repository Example with MapperlyCrudAppService

This example shows how to implement a complete repository pattern that works with the `MapperlyCrudAppService` base class for handling nested relationships with EF Core includes.

## 1. Domain Layer - Repository Interface

```csharp
// src/Imip.Ekb.Domain/Products/IProductRepository.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Products
{
    public interface IProductRepository : IRepository<Product, Guid>
    {
        /// <summary>
        /// Gets a product with all related data for detail view
        /// Includes: Category, ProductImages, ProductAttributes, Reviews with Customer
        /// </summary>
        Task<Product> GetWithDetailsAsync(Guid id);

        /// <summary>
        /// Gets a product with minimal related data for edit operations
        /// Includes: Category, ProductAttributes (without Reviews for performance)
        /// </summary>
        Task<Product> GetForEditAsync(Guid id);

        /// <summary>
        /// Gets products with basic includes optimized for list views
        /// Includes: Category, First ProductImage only
        /// </summary>
        Task<List<Product>> GetListWithBasicIncludesAsync();

        /// <summary>
        /// Gets products by category with optimized includes
        /// </summary>
        Task<List<Product>> GetByCategoryAsync(Guid categoryId);

        /// <summary>
        /// Gets products with their reviews for a specific date range
        /// </summary>
        Task<List<Product>> GetWithRecentReviewsAsync(DateTime fromDate);

        /// <summary>
        /// Gets products with stock information
        /// Includes: Category, ProductImages, Stock information
        /// </summary>
        Task<List<Product>> GetWithStockInfoAsync();

        /// <summary>
        /// Search products with includes based on search criteria
        /// </summary>
        Task<List<Product>> SearchProductsAsync(string searchTerm, Guid? categoryId = null);
    }
}
```

## 2. EntityFrameworkCore Layer - Repository Implementation

```csharp
// src/Imip.Ekb.EntityFrameworkCore/Products/EfCoreProductRepository.cs
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Imip.Ekb.EntityFrameworkCore;

namespace Imip.Ekb.Products
{
    public class EfCoreProductRepository : EfCoreRepository<EkbDbContext, Product, Guid>, IProductRepository
    {
        public EfCoreProductRepository(IDbContextProvider<EkbDbContext> dbContextProvider)
            : base(dbContextProvider)
        {
        }

        public async Task<Product> GetWithDetailsAsync(Guid id)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductImages)
                .Include(p => p.ProductAttributes)
                    .ThenInclude(pa => pa.AttributeValue)
                .Include(p => p.Reviews.Take(10)) // Limit reviews for performance
                    .ThenInclude(r => r.Customer)
                .Include(p => p.Supplier)
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<Product> GetForEditAsync(Guid id)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductAttributes)
                    .ThenInclude(pa => pa.AttributeValue)
                .Include(p => p.Supplier)
                // Don't include Reviews and limit ProductImages for edit performance
                .Include(p => p.ProductImages.Take(5))
                .FirstOrDefaultAsync(p => p.Id == id);
        }

        public async Task<List<Product>> GetListWithBasicIncludesAsync()
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductImages.Take(1)) // Only first image for list view
                .Where(p => p.IsActive) // Only active products
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Product>> GetByCategoryAsync(Guid categoryId)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductImages.Take(1))
                .Where(p => p.CategoryId == categoryId && p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Product>> GetWithRecentReviewsAsync(DateTime fromDate)
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductImages.Take(1))
                .Include(p => p.Reviews.Where(r => r.CreationTime >= fromDate))
                    .ThenInclude(r => r.Customer)
                .Where(p => p.Reviews.Any(r => r.CreationTime >= fromDate))
                .OrderByDescending(p => p.Reviews.Count(r => r.CreationTime >= fromDate))
                .ToListAsync();
        }

        public async Task<List<Product>> GetWithStockInfoAsync()
        {
            var dbSet = await GetDbSetAsync();
            return await dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductImages.Take(1))
                .Include(p => p.StockItems)
                .Where(p => p.IsActive)
                .OrderBy(p => p.Name)
                .ToListAsync();
        }

        public async Task<List<Product>> SearchProductsAsync(string searchTerm, Guid? categoryId = null)
        {
            var dbSet = await GetDbSetAsync();
            var query = dbSet
                .Include(p => p.Category)
                .Include(p => p.ProductImages.Take(1))
                .Where(p => p.IsActive);

            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                query = query.Where(p => 
                    p.Name.Contains(searchTerm) || 
                    p.Description.Contains(searchTerm) ||
                    p.Category.Name.Contains(searchTerm));
            }

            if (categoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == categoryId.Value);
            }

            return await query
                .OrderBy(p => p.Name)
                .ToListAsync();
        }
    }
}
```

## 3. Application Layer - Product Application Service

```csharp
// src/Imip.Ekb.Application/Products/ProductAppService.cs
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Imip.Ekb.Permissions;
using Imip.Ekb.Services;
using Imip.Ekb.Mapping;

namespace Imip.Ekb.Products
{
    [Authorize(EkbPermissions.Products.Default)]
    public class ProductAppService : 
        MapperlyCrudAppService<
            Product,                     // The entity
            ProductDto,                  // Used to show products
            Guid,                        // Primary key type
            GetProductListDto,           // Used for paging/sorting/filtering
            CreateUpdateProductDto,      // Used to create/update a product
            ProductMapper>,              // The Mapperly mapper
        IProductAppService               // The interface
    {
        private readonly IProductRepository _productRepository;

        public ProductAppService(
            IProductRepository repository,
            ProductMapper productMapper)
            : base(repository, productMapper)
        {
            _productRepository = repository;
            
            // Configure permissions
            GetPolicyName = EkbPermissions.Products.Default;
            GetListPolicyName = EkbPermissions.Products.Default;
            CreatePolicyName = EkbPermissions.Products.Create;
            UpdatePolicyName = EkbPermissions.Products.Edit;
            DeletePolicyName = EkbPermissions.Products.Delete;
        }

        // Override to use repository method with full details
        public override async Task<ProductDto> GetAsync(Guid id)
        {
            var product = await _productRepository.GetWithDetailsAsync(id);
            return MapToGetOutputDto(product);
        }

        // Custom method for edit scenarios with optimized includes
        public async Task<ProductDto> GetForEditAsync(Guid id)
        {
            var product = await _productRepository.GetForEditAsync(id);
            return MapToGetOutputDto(product);
        }

        // Override list method to use optimized includes
        public override async Task<PagedResultDto<ProductDto>> GetListAsync(GetProductListDto input)
        {
            // For simple lists, use the optimized repository method
            if (string.IsNullOrEmpty(input.Filter) && !input.CategoryId.HasValue)
            {
                var products = await _productRepository.GetListWithBasicIncludesAsync();
                var totalCount = products.Count;
                
                // Apply paging
                var pagedProducts = products
                    .Skip(input.SkipCount)
                    .Take(input.MaxResultCount)
                    .ToList();

                var dtos = MapEntitiesToDtos(pagedProducts);
                return new PagedResultDto<ProductDto>(totalCount, dtos);
            }

            // For filtered lists, use search method
            var searchResults = await _productRepository.SearchProductsAsync(input.Filter, input.CategoryId);
            var searchTotalCount = searchResults.Count;
            
            var pagedSearchResults = searchResults
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount)
                .ToList();

            var searchDtos = MapEntitiesToDtos(pagedSearchResults);
            return new PagedResultDto<ProductDto>(searchTotalCount, searchDtos);
        }

        // Custom methods using specific repository methods
        public async Task<List<ProductDto>> GetByCategoryAsync(Guid categoryId)
        {
            var products = await _productRepository.GetByCategoryAsync(categoryId);
            return MapEntitiesToDtos(products);
        }

        public async Task<List<ProductDto>> GetWithRecentReviewsAsync(DateTime fromDate)
        {
            var products = await _productRepository.GetWithRecentReviewsAsync(fromDate);
            return MapEntitiesToDtos(products);
        }

        public async Task<List<ProductDto>> GetWithStockInfoAsync()
        {
            var products = await _productRepository.GetWithStockInfoAsync();
            return MapEntitiesToDtos(products);
        }

        // Example of using projection for read-only scenarios
        public async Task<PagedResultDto<ProductSummaryDto>> GetProductSummariesAsync(GetProductListDto input)
        {
            var query = await Repository.GetQueryableAsync();
            
            // Use projection for better performance
            var projectionQuery = query
                .Where(p => p.IsActive)
                .Select(p => new ProductSummaryDto
                {
                    Id = p.Id,
                    Name = p.Name,
                    Price = p.Price,
                    CategoryName = p.Category.Name,
                    ImageUrl = p.ProductImages.FirstOrDefault().Url,
                    IsInStock = p.StockItems.Any(s => s.Quantity > 0),
                    ReviewCount = p.Reviews.Count(),
                    AverageRating = p.Reviews.Any() ? p.Reviews.Average(r => r.Rating) : 0
                });

            var totalCount = await AsyncExecuter.CountAsync(projectionQuery);
            
            projectionQuery = projectionQuery
                .Skip(input.SkipCount)
                .Take(input.MaxResultCount);

            var dtos = await AsyncExecuter.ToListAsync(projectionQuery);
            return new PagedResultDto<ProductSummaryDto>(totalCount, dtos);
        }
    }
}
```

## 4. Registration in Module

```csharp
// src/Imip.Ekb.EntityFrameworkCore/EkbEntityFrameworkCoreModule.cs
[DependsOn(typeof(EkbDomainModule))]
public class EkbEntityFrameworkCoreModule : AbpModule
{
    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        // ... other configurations

        // Register custom repositories
        context.Services.AddTransient<IProductRepository, EfCoreProductRepository>();
    }
}
```

## 5. Key Benefits of This Implementation

### ✅ **Performance Optimized:**
- **Detail View**: Full includes for comprehensive data
- **Edit View**: Optimized includes without heavy data like reviews
- **List View**: Minimal includes with only first image
- **Search**: Context-aware includes based on search criteria

### ✅ **ABP Compliant:**
- Repository interfaces in Domain layer
- EF Core implementations in EntityFrameworkCore layer
- Application logic in Application layer
- Proper separation of concerns

### ✅ **Mapperly Integration:**
- Zero code duplication with `MapperlyCrudAppService` base class
- Automatic compile-time mapping
- Type-safe mapping operations

### ✅ **Flexible and Maintainable:**
- Context-specific repository methods
- Easy to add new include scenarios
- Testable through repository abstractions
- Performance monitoring capabilities

This implementation provides a complete, production-ready example of how to properly integrate EF Core includes with Mapperly in ABP Framework while maintaining architectural best practices.
