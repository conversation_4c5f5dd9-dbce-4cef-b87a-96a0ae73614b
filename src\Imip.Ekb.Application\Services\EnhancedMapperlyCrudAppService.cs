using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Services;

/// <summary>
/// Enhanced CRUD Application Service that supports complex nested relationships with Mapperly
/// Provides additional methods for handling complex mapping scenarios
/// </summary>
/// <typeparam name="TEntity">The entity type</typeparam>
/// <typeparam name="TEntityDto">The entity DTO type</typeparam>
/// <typeparam name="TKey">The primary key type</typeparam>
/// <typeparam name="TGetListInput">The input type for GetList operation</typeparam>
/// <typeparam name="TCreateInput">The input type for Create operation</typeparam>
/// <typeparam name="TUpdateInput">The input type for Update operation</typeparam>
/// <typeparam name="TMapper">The Mapperly mapper type</typeparam>
public abstract class EnhancedMapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper> :
    MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateInput : class
    where TUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
{
    protected EnhancedMapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository, mapper)
    {
    }

    #region Enhanced Mapping Methods for Complex Scenarios

    /// <summary>
    /// Maps entity to DTO with custom include strategy for nested relationships
    /// Override this method to handle complex nested mappings
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <param name="includeNestedEntities">Whether to include nested entities</param>
    /// <param name="maxDepth">Maximum depth for nested mapping to prevent circular references</param>
    /// <returns>Mapped DTO</returns>
    protected virtual TEntityDto MapToEntityDtoWithIncludes(TEntity entity, bool includeNestedEntities = true, int maxDepth = 3)
    {
        // Default implementation uses the standard mapper
        // Override in derived classes for complex scenarios
        return Mapper.ToDto(entity);
    }

    /// <summary>
    /// Maps entities to DTOs with optimized projection for list scenarios
    /// Override this method to provide performance-optimized mapping for lists
    /// </summary>
    /// <param name="entities">The entities to map</param>
    /// <param name="includeNestedEntities">Whether to include nested entities</param>
    /// <returns>Mapped DTOs</returns>
    protected virtual List<TEntityDto> MapToEntityDtosWithProjection(List<TEntity> entities, bool includeNestedEntities = false)
    {
        // Default implementation uses the standard mapper
        // Override in derived classes for optimized projections
        return Mapper.ToDto(entities);
    }

    /// <summary>
    /// Maps entity to DTO with conditional nested loading based on context
    /// Useful for scenarios where nested data should only be loaded in certain contexts
    /// </summary>
    /// <param name="entity">The entity to map</param>
    /// <param name="context">The mapping context (e.g., "detail", "list", "summary")</param>
    /// <returns>Mapped DTO</returns>
    protected virtual TEntityDto MapToEntityDtoWithContext(TEntity entity, string context = "default")
    {
        return context switch
        {
            "detail" => MapToEntityDtoWithIncludes(entity, includeNestedEntities: true, maxDepth: 5),
            "list" => MapToEntityDtoWithIncludes(entity, includeNestedEntities: false),
            "summary" => MapToEntityDtoWithIncludes(entity, includeNestedEntities: false),
            _ => Mapper.ToDto(entity)
        };
    }

    /// <summary>
    /// Handles circular reference prevention during mapping
    /// Override this method to implement custom circular reference handling
    /// </summary>
    /// <param name="entity">The entity being mapped</param>
    /// <param name="visitedEntities">Set of already visited entity IDs</param>
    /// <param name="currentDepth">Current mapping depth</param>
    /// <param name="maxDepth">Maximum allowed depth</param>
    /// <returns>Mapped DTO or null if circular reference detected</returns>
    protected virtual TEntityDto? MapWithCircularReferenceCheck(TEntity entity, HashSet<TKey> visitedEntities, int currentDepth, int maxDepth)
    {
        if (currentDepth >= maxDepth || visitedEntities.Contains(entity.Id))
        {
            return null; // Prevent circular reference or max depth exceeded
        }

        visitedEntities.Add(entity.Id);
        var result = Mapper.ToDto(entity);
        visitedEntities.Remove(entity.Id);

        return result;
    }

    #endregion

    #region Enhanced CRUD Operations with Nested Support

    /// <summary>
    /// Gets entity with detailed nested relationships
    /// </summary>
    public virtual async Task<TEntityDto> GetWithDetailsAsync(TKey id)
    {
        var entity = await Repository.GetAsync(id);
        return MapToEntityDtoWithContext(entity, "detail");
    }

    /// <summary>
    /// Gets entities with optimized projection for list views
    /// </summary>
    public virtual async Task<PagedResultDto<TEntityDto>> GetListWithProjectionAsync(TGetListInput input)
    {
        var query = await CreateFilteredQueryAsync(input);
        var totalCount = await AsyncExecuter.CountAsync(query);

        query = ApplySorting(query, input);
        query = ApplyPaging(query, input);

        var entities = await AsyncExecuter.ToListAsync(query);
        var dtos = MapToEntityDtosWithProjection(entities, includeNestedEntities: false);

        return new PagedResultDto<TEntityDto>(totalCount, dtos);
    }

    /// <summary>
    /// Creates entity with nested relationship handling
    /// Override this method to handle complex nested entity creation
    /// </summary>
    public override async Task<TEntityDto> CreateAsync(TCreateInput input)
    {
        var entity = await MapToEntityAsync(input);
        
        // Handle nested entities before insertion
        await HandleNestedEntitiesOnCreateAsync(entity, input);
        
        await Repository.InsertAsync(entity);
        
        return MapToEntityDtoWithContext(entity, "detail");
    }

    /// <summary>
    /// Updates entity with nested relationship handling
    /// Override this method to handle complex nested entity updates
    /// </summary>
    public override async Task<TEntityDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        var entity = await Repository.GetAsync(id);
        
        // Handle nested entities before update
        await HandleNestedEntitiesOnUpdateAsync(entity, input);
        
        await MapToEntityAsync(input, entity);
        await Repository.UpdateAsync(entity);
        
        return MapToEntityDtoWithContext(entity, "detail");
    }

    #endregion

    #region Virtual Methods for Nested Entity Handling

    /// <summary>
    /// Override this method to handle nested entities during creation
    /// </summary>
    protected virtual Task HandleNestedEntitiesOnCreateAsync(TEntity entity, TCreateInput input)
    {
        // Default implementation does nothing
        // Override in derived classes to handle nested entities
        return Task.CompletedTask;
    }

    /// <summary>
    /// Override this method to handle nested entities during update
    /// </summary>
    protected virtual Task HandleNestedEntitiesOnUpdateAsync(TEntity entity, TUpdateInput input)
    {
        // Default implementation does nothing
        // Override in derived classes to handle nested entities
        return Task.CompletedTask;
    }

    /// <summary>
    /// Override this method to provide custom entity mapping with async support
    /// Useful for scenarios where you need to load related data during mapping
    /// </summary>
    protected virtual Task<TEntity> MapToEntityAsync(TCreateInput input)
    {
        return Task.FromResult(Mapper.ToEntity(input));
    }

    /// <summary>
    /// Override this method to provide custom entity update mapping with async support
    /// </summary>
    protected virtual Task MapToEntityAsync(TUpdateInput input, TEntity entity)
    {
        Mapper.UpdateEntity(input, entity);
        return Task.CompletedTask;
    }

    #endregion

    #region Helper Methods for Complex Scenarios

    /// <summary>
    /// Helper method to check if nested entities should be loaded based on context
    /// </summary>
    protected virtual bool ShouldIncludeNestedEntities(string context)
    {
        return context switch
        {
            "detail" => true,
            "edit" => true,
            "list" => false,
            "summary" => false,
            _ => false
        };
    }

    /// <summary>
    /// Helper method to get maximum depth for nested mapping based on context
    /// </summary>
    protected virtual int GetMaxDepthForContext(string context)
    {
        return context switch
        {
            "detail" => 5,
            "edit" => 3,
            "list" => 1,
            "summary" => 1,
            _ => 2
        };
    }

    #endregion
}

/// <summary>
/// Simplified enhanced version for cases where Create and Update DTOs are the same
/// </summary>
public abstract class EnhancedMapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TMapper> :
    EnhancedMapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TCreateUpdateInput, TMapper>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateUpdateInput, TCreateUpdateInput>
{
    protected EnhancedMapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository, mapper)
    {
    }
}
