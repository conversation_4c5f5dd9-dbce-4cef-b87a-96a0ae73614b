using System;
using System.Collections.Generic;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Domain.Entities;

namespace Imip.Ekb.Mapping;

/// <summary>
/// Base interface for ABP-compatible Mapperly mappers
/// Provides common mapping operations for ABP entities and DTOs
/// </summary>
/// <typeparam name="TEntity">The domain entity type</typeparam>
/// <typeparam name="TEntityDto">The entity DTO type</typeparam>
/// <typeparam name="TKey">The primary key type</typeparam>
/// <typeparam name="TCreateDto">The create DTO type</typeparam>
/// <typeparam name="TUpdateDto">The update DTO type</typeparam>
public interface IAbpMapper<TEntity, TEntityDto, TKey, TCreateDto, TUpdateDto>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TCreateDto : class
    where TUpdateDto : class
{
    /// <summary>
    /// Maps entity to DTO
    /// </summary>
    TEntityDto ToDto(TEntity entity);

    /// <summary>
    /// Maps list of entities to list of DTOs
    /// </summary>
    List<TEntityDto> ToDto(List<TEntity> entities);

    /// <summary>
    /// Maps create DTO to entity
    /// </summary>
    TEntity ToEntity(TCreateDto createDto);

    /// <summary>
    /// Maps update DTO to existing entity (partial mapping)
    /// </summary>
    void UpdateEntity(TUpdateDto updateDto, TEntity entity);

    /// <summary>
    /// Maps create DTO to entity with specified ID
    /// </summary>
    TEntity ToEntity(TCreateDto createDto, TKey id);
}

/// <summary>
/// Simplified interface for cases where create and update DTOs are the same
/// </summary>
/// <typeparam name="TEntity">The domain entity type</typeparam>
/// <typeparam name="TEntityDto">The entity DTO type</typeparam>
/// <typeparam name="TKey">The primary key type</typeparam>
/// <typeparam name="TCreateUpdateDto">The create/update DTO type</typeparam>
public interface IAbpMapper<TEntity, TEntityDto, TKey, TCreateUpdateDto> : 
    IAbpMapper<TEntity, TEntityDto, TKey, TCreateUpdateDto, TCreateUpdateDto>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TCreateUpdateDto : class
{
}
