using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Volo.Abp.Domain.Repositories.EntityFrameworkCore;
using Volo.Abp.EntityFrameworkCore;
using Imip.Ekb.EntityFrameworkCore;

namespace Imip.Ekb.Products;

/// <summary>
/// EF Core implementation of IProductRepository with optimized includes for different scenarios
/// Follows ABP Framework's repository pattern with performance optimizations
/// </summary>
public class EfCoreProductRepository : EfCoreRepository<EkbDbContext, Product, Guid>, IProductRepository
{
    public EfCoreProductRepository(IDbContextProvider<EkbDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<Product?> GetWithDetailsAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.ProductAttributes)
                .ThenInclude(pa => pa.AttributeValue)
            .Include(p => p.Reviews.Take(10)) // Limit reviews for performance
                .ThenInclude(r => r.Customer)
            .Include(p => p.Supplier)
            .AsSplitQuery() // Use split query for multiple includes to avoid cartesian explosion
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<Product?> GetForEditAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductAttributes)
                .ThenInclude(pa => pa.AttributeValue)
            .Include(p => p.Supplier)
            // Don't include Reviews and limit ProductImages for edit performance
            .Include(p => p.ProductImages.Take(5))
            .AsSplitQuery()
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<List<Product>> GetListWithBasicIncludesAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1)) // Only first image for list view
            .Where(p => p.IsActive) // Only active products
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<List<Product>> GetByCategoryAsync(Guid categoryId)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1))
            .Where(p => p.CategoryId == categoryId && p.IsActive)
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<List<Product>> GetWithRecentReviewsAsync(DateTime fromDate)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1))
            .Include(p => p.Reviews.Where(r => r.CreationTime >= fromDate))
                .ThenInclude(r => r.Customer)
            .Where(p => p.Reviews.Any(r => r.CreationTime >= fromDate))
            .OrderByDescending(p => p.Reviews.Count(r => r.CreationTime >= fromDate))
            .AsSplitQuery()
            .ToListAsync();
    }

    public async Task<List<Product>> GetWithStockInfoAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1))
            .Include(p => p.StockItems)
            .Where(p => p.IsActive)
            .OrderBy(p => p.Name)
            .AsSplitQuery()
            .ToListAsync();
    }

    public async Task<List<Product>> SearchProductsAsync(string searchTerm, Guid? categoryId = null)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1))
            .Where(p => p.IsActive);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLower();
            query = query.Where(p => 
                p.Name.ToLower().Contains(lowerSearchTerm) || 
                p.Description.ToLower().Contains(lowerSearchTerm) ||
                p.Category.Name.ToLower().Contains(lowerSearchTerm));
        }

        if (categoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == categoryId.Value);
        }

        return await query
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    public async Task<List<Product>> GetPagedListWithIncludesAsync(int skipCount, int maxResultCount, string sorting = null)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1))
            .Where(p => p.IsActive);

        // Apply sorting
        if (!string.IsNullOrWhiteSpace(sorting))
        {
            // Simple sorting implementation - in production, consider using Dynamic LINQ
            query = sorting.ToLower() switch
            {
                "name" => query.OrderBy(p => p.Name),
                "name desc" => query.OrderByDescending(p => p.Name),
                "price" => query.OrderBy(p => p.Price),
                "price desc" => query.OrderByDescending(p => p.Price),
                "creationtime" => query.OrderBy(p => p.CreationTime),
                "creationtime desc" => query.OrderByDescending(p => p.CreationTime),
                _ => query.OrderBy(p => p.Name)
            };
        }
        else
        {
            query = query.OrderBy(p => p.Name);
        }

        return await query
            .Skip(skipCount)
            .Take(maxResultCount)
            .ToListAsync();
    }

    public async Task<int> GetSearchCountAsync(string searchTerm, Guid? categoryId = null)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.Where(p => p.IsActive);

        if (!string.IsNullOrWhiteSpace(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLower();
            query = query.Where(p => 
                p.Name.ToLower().Contains(lowerSearchTerm) || 
                p.Description.ToLower().Contains(lowerSearchTerm) ||
                p.Category.Name.ToLower().Contains(lowerSearchTerm));
        }

        if (categoryId.HasValue)
        {
            query = query.Where(p => p.CategoryId == categoryId.Value);
        }

        return await query.CountAsync();
    }

    /// <summary>
    /// Override the default GetQueryableAsync to provide a base query with common filters
    /// This can be used by the base CrudAppService for standard operations
    /// </summary>
    public override async Task<IQueryable<Product>> GetQueryableAsync()
    {
        var query = await base.GetQueryableAsync();
        
        // Apply common filters that should always be applied
        return query.Where(p => p.IsActive);
    }

    /// <summary>
    /// Get queryable with includes for custom scenarios
    /// Use this when you need to build custom queries with includes
    /// </summary>
    /// <param name="includeDetails">Whether to include detailed related data</param>
    /// <returns>Queryable with appropriate includes</returns>
    public async Task<IQueryable<Product>> GetQueryableWithIncludesAsync(bool includeDetails = false)
    {
        var dbSet = await GetDbSetAsync();
        var query = dbSet.Where(p => p.IsActive);

        if (includeDetails)
        {
            query = query
                .Include(p => p.Category)
                .Include(p => p.ProductImages)
                .Include(p => p.ProductAttributes)
                    .ThenInclude(pa => pa.AttributeValue)
                .Include(p => p.Supplier)
                .AsSplitQuery();
        }
        else
        {
            query = query
                .Include(p => p.Category)
                .Include(p => p.ProductImages.Take(1));
        }

        return query;
    }

    /// <summary>
    /// Bulk operations for better performance when dealing with multiple products
    /// </summary>
    /// <param name="productIds">List of product IDs</param>
    /// <returns>Products with basic includes</returns>
    public async Task<List<Product>> GetManyWithBasicIncludesAsync(List<Guid> productIds)
    {
        if (!productIds.Any())
            return new List<Product>();

        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1))
            .Where(p => productIds.Contains(p.Id) && p.IsActive)
            .ToListAsync();
    }
}
