import{j as e,r as i,m as A,Y as T}from"./App-Dy45zejD.js";import{e as R,d as D,g as ke,P as F,u as ge,h as ie,i as U,B as g,C as q,a as L,b as O,c as V}from"./card-D9JhgxcF.js";import{u as Ce,e as ye,C as Ne,I as we,S as X,a as G,b as K,c as Y,d as I}from"./select-ZRFupiEN.js";import{P as Se,A as J,a as Q,T as W,b as Z,c as ee,d as te,e as se,f as ae,g as re,h as ne,B as Ae,S as De,U as Ee,i as _e}from"./alert-dialog-1PIkbgR7.js";/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ie=[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]],Pe=R("download",Ie);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Te=[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Re=R("eye",Te);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Me=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],Be=R("plus",Me);/**
 * @license lucide-react v0.511.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ze=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Fe=R("search",ze);function He({className:s,...a}){return e.jsx("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:e.jsx("table",{"data-slot":"table",className:D("w-full caption-bottom text-sm",s),...a})})}function $e({className:s,...a}){return e.jsx("thead",{"data-slot":"table-header",className:D("[&_tr]:border-b",s),...a})}function Ue({className:s,...a}){return e.jsx("tbody",{"data-slot":"table-body",className:D("[&_tr:last-child]:border-0",s),...a})}function ce({className:s,...a}){return e.jsx("tr",{"data-slot":"table-row",className:D("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",s),...a})}function C({className:s,...a}){return e.jsx("th",{"data-slot":"table-head",className:D("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}function y({className:s,...a}){return e.jsx("td",{"data-slot":"table-cell",className:D("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",s),...a})}var M="Checkbox",[qe,Je]=ke(M),[Le,H]=qe(M);function Oe(s){const{__scopeCheckbox:a,checked:x,children:j,defaultChecked:o,disabled:r,form:p,name:v,onCheckedChange:n,required:c,value:u="on",internal_do_not_use_render:d}=s,[f,b]=ge({prop:x,defaultProp:o??!1,onChange:n,caller:M}),[k,m]=i.useState(null),[w,l]=i.useState(null),h=i.useRef(!1),E=k?!!p||!!k.closest("form"):!0,_={checked:f,disabled:r,setChecked:b,control:k,setControl:m,name:v,form:p,value:u,hasConsumerStoppedPropagationRef:h,required:c,defaultChecked:N(o)?!1:o,isFormControl:E,bubbleInput:w,setBubbleInput:l};return e.jsx(Le,{scope:a,..._,children:Ve(d)?d(_):j})}var oe="CheckboxTrigger",de=i.forwardRef(({__scopeCheckbox:s,onKeyDown:a,onClick:x,...j},o)=>{const{control:r,value:p,disabled:v,checked:n,required:c,setControl:u,setChecked:d,hasConsumerStoppedPropagationRef:f,isFormControl:b,bubbleInput:k}=H(oe,s),m=ie(o,u),w=i.useRef(n);return i.useEffect(()=>{const l=r==null?void 0:r.form;if(l){const h=()=>d(w.current);return l.addEventListener("reset",h),()=>l.removeEventListener("reset",h)}},[r,d]),e.jsx(F.button,{type:"button",role:"checkbox","aria-checked":N(n)?"mixed":n,"aria-required":c,"data-state":pe(n),"data-disabled":v?"":void 0,disabled:v,value:p,...j,ref:m,onKeyDown:U(a,l=>{l.key==="Enter"&&l.preventDefault()}),onClick:U(x,l=>{d(h=>N(h)?!0:!h),k&&b&&(f.current=l.isPropagationStopped(),f.current||l.stopPropagation())})})});de.displayName=oe;var he=i.forwardRef((s,a)=>{const{__scopeCheckbox:x,name:j,checked:o,defaultChecked:r,required:p,disabled:v,value:n,onCheckedChange:c,form:u,...d}=s;return e.jsx(Oe,{__scopeCheckbox:x,checked:o,defaultChecked:r,disabled:v,required:p,onCheckedChange:c,name:j,form:u,value:n,internal_do_not_use_render:({isFormControl:f})=>e.jsxs(e.Fragment,{children:[e.jsx(de,{...d,ref:a,__scopeCheckbox:x}),f&&e.jsx(je,{__scopeCheckbox:x})]})})});he.displayName=M;var xe="CheckboxIndicator",ue=i.forwardRef((s,a)=>{const{__scopeCheckbox:x,forceMount:j,...o}=s,r=H(xe,x);return e.jsx(Se,{present:j||N(r.checked)||r.checked===!0,children:e.jsx(F.span,{"data-state":pe(r.checked),"data-disabled":r.disabled?"":void 0,...o,ref:a,style:{pointerEvents:"none",...s.style}})})});ue.displayName=xe;var me="CheckboxBubbleInput",je=i.forwardRef(({__scopeCheckbox:s,...a},x)=>{const{control:j,hasConsumerStoppedPropagationRef:o,checked:r,defaultChecked:p,required:v,disabled:n,name:c,value:u,form:d,bubbleInput:f,setBubbleInput:b}=H(me,s),k=ie(x,b),m=Ce(r),w=ye(j);i.useEffect(()=>{const h=f;if(!h)return;const E=window.HTMLInputElement.prototype,P=Object.getOwnPropertyDescriptor(E,"checked").set,B=!o.current;if(m!==r&&P){const z=new Event("click",{bubbles:B});h.indeterminate=N(r),P.call(h,N(r)?!1:r),h.dispatchEvent(z)}},[f,m,r,o]);const l=i.useRef(N(r)?!1:r);return e.jsx(F.input,{type:"checkbox","aria-hidden":!0,defaultChecked:p??l.current,required:v,disabled:n,name:c,value:u,form:d,...a,tabIndex:-1,ref:k,style:{...a.style,...w,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});je.displayName=me;function Ve(s){return typeof s=="function"}function N(s){return s==="indeterminate"}function pe(s){return N(s)?"indeterminate":s?"checked":"unchecked"}function le({className:s,...a}){return e.jsx(he,{"data-slot":"checkbox",className:D("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...a,children:e.jsx(ue,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:e.jsx(Ne,{className:"size-3.5"})})})}const Qe=({employees:s,totalCount:a,currentPage:x,pageSize:j,filter:o,department:r,isActive:p,departments:v,permissions:n})=>{const[c,u]=i.useState([]),[d,f]=i.useState(o),[b,k]=i.useState(r||"all"),[m,w]=i.useState(p===void 0?"all":p?"active":"inactive"),l=()=>{A.get("/employees",{filter:d,department:b==="all"?void 0:b,isActive:m==="all"?void 0:m==="active"})},h=t=>{u(t?s.map(S=>S.id):[])},E=(t,S)=>{u(S?[...c,t]:c.filter(ve=>ve!==t))},_=()=>{A.post("/employees/delete-multiple",{ids:c})},P=t=>{A.post(`/employees/${t}/delete`)},B=t=>{A.post(`/employees/${t}/activate`)},z=t=>{A.post(`/employees/${t}/deactivate`)},fe=t=>new Intl.NumberFormat("en-US",{style:"currency",currency:"USD"}).format(t),be=t=>new Date(t).toLocaleDateString(),$=Math.ceil(a/j);return e.jsxs("div",{className:"container mx-auto p-6 space-y-6",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Employees"}),e.jsx("p",{className:"text-gray-600 mt-1",children:"Manage your organization's employees"})]}),e.jsxs("div",{className:"flex gap-2",children:[n.export&&e.jsxs(g,{variant:"outline",onClick:()=>A.get("/employees/export"),children:[e.jsx(Pe,{className:"w-4 h-4 mr-2"}),"Export"]}),n.create&&e.jsx(T,{href:"/employees/create",children:e.jsxs(g,{children:[e.jsx(Be,{className:"w-4 h-4 mr-2"}),"Add Employee"]})})]})]}),e.jsxs(q,{children:[e.jsx(L,{children:e.jsx(O,{children:"Search & Filter"})}),e.jsx(V,{children:e.jsxs("div",{className:"flex gap-4 items-end",children:[e.jsx("div",{className:"flex-1",children:e.jsx(we,{placeholder:"Search employees...",value:d,onChange:t=>f(t.target.value),className:"w-full"})}),e.jsx("div",{className:"w-48",children:e.jsxs(X,{value:b,onValueChange:k,children:[e.jsx(G,{children:e.jsx(K,{placeholder:"All Departments"})}),e.jsxs(Y,{children:[e.jsx(I,{value:"all",children:"All Departments"}),v.map(t=>e.jsx(I,{value:t,children:t},t))]})]})}),e.jsx("div",{className:"w-32",children:e.jsxs(X,{value:m,onValueChange:w,children:[e.jsx(G,{children:e.jsx(K,{})}),e.jsxs(Y,{children:[e.jsx(I,{value:"all",children:"All"}),e.jsx(I,{value:"active",children:"Active"}),e.jsx(I,{value:"inactive",children:"Inactive"})]})]})}),e.jsxs(g,{onClick:l,children:[e.jsx(Fe,{className:"w-4 h-4 mr-2"}),"Search"]})]})})]}),e.jsxs(q,{children:[e.jsx(L,{children:e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsxs(O,{children:["Employees (",a,")"]}),c.length>0&&n.delete&&e.jsxs(J,{children:[e.jsx(Q,{asChild:!0,children:e.jsxs(g,{variant:"destructive",size:"sm",children:[e.jsx(W,{className:"w-4 h-4 mr-2"}),"Delete Selected (",c.length,")"]})}),e.jsxs(Z,{children:[e.jsxs(ee,{children:[e.jsx(te,{children:"Delete Employees"}),e.jsxs(se,{children:["Are you sure you want to delete ",c.length," employee(s)? This action cannot be undone."]})]}),e.jsxs(ae,{children:[e.jsx(re,{children:"Cancel"}),e.jsx(ne,{onClick:_,children:"Delete"})]})]})]})]})}),e.jsxs(V,{children:[e.jsxs(He,{children:[e.jsx($e,{children:e.jsxs(ce,{children:[e.jsx(C,{className:"w-12",children:e.jsx(le,{checked:c.length===s.length&&s.length>0,onCheckedChange:h})}),e.jsx(C,{children:"Name"}),e.jsx(C,{children:"Email"}),e.jsx(C,{children:"Department"}),e.jsx(C,{children:"Position"}),e.jsx(C,{children:"Hire Date"}),e.jsx(C,{children:"Salary"}),e.jsx(C,{children:"Status"}),e.jsx(C,{className:"text-right",children:"Actions"})]})}),e.jsx(Ue,{children:s.map(t=>e.jsxs(ce,{children:[e.jsx(y,{children:e.jsx(le,{checked:c.includes(t.id),onCheckedChange:S=>E(t.id,S)})}),e.jsx(y,{className:"font-medium",children:t.fullName}),e.jsx(y,{children:t.email}),e.jsx(y,{children:t.department}),e.jsx(y,{children:t.position}),e.jsx(y,{children:be(t.hireDate)}),e.jsx(y,{children:fe(t.salary)}),e.jsx(y,{children:e.jsx(Ae,{variant:t.isActive?"default":"secondary",children:t.isActive?"Active":"Inactive"})}),e.jsx(y,{className:"text-right",children:e.jsxs("div",{className:"flex justify-end gap-1",children:[e.jsx(T,{href:`/employees/${t.id}`,children:e.jsx(g,{variant:"ghost",size:"sm",children:e.jsx(Re,{className:"w-4 h-4"})})}),n.edit&&e.jsx(T,{href:`/employees/${t.id}/edit`,children:e.jsx(g,{variant:"ghost",size:"sm",children:e.jsx(De,{className:"w-4 h-4"})})}),n.manageActivation&&e.jsx(g,{variant:"ghost",size:"sm",onClick:()=>t.isActive?z(t.id):B(t.id),children:t.isActive?e.jsx(Ee,{className:"w-4 h-4"}):e.jsx(_e,{className:"w-4 h-4"})}),n.delete&&e.jsxs(J,{children:[e.jsx(Q,{asChild:!0,children:e.jsx(g,{variant:"ghost",size:"sm",children:e.jsx(W,{className:"w-4 h-4"})})}),e.jsxs(Z,{children:[e.jsxs(ee,{children:[e.jsx(te,{children:"Delete Employee"}),e.jsxs(se,{children:["Are you sure you want to delete ",t.fullName,"? This action cannot be undone."]})]}),e.jsxs(ae,{children:[e.jsx(re,{children:"Cancel"}),e.jsx(ne,{onClick:()=>P(t.id),children:"Delete"})]})]})]})]})})]},t.id))})]}),s.length===0&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:["No employees found. ",n.create&&e.jsx(T,{href:"/employees/create",className:"text-blue-600 hover:underline",children:"Create your first employee"})]}),$>1&&e.jsx("div",{className:"flex justify-center mt-6",children:e.jsx("div",{className:"flex gap-2",children:Array.from({length:$},(t,S)=>S+1).map(t=>e.jsx(g,{variant:t===x?"default":"outline",size:"sm",onClick:()=>A.get("/employees",{page:t,filter:d,department:b==="all"?void 0:b,isActive:m==="all"?void 0:m==="active"}),children:t},t))})})]})]})]})};export{Qe as default};
