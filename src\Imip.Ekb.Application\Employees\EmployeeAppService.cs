using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Imip.Ekb.Permissions;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Employees;

[Authorize(EkbPermissions.Employees.Default)]
public class EmployeeAppService : MapperlyCrudAppService<
        Employee,
        EmployeeDto,
        Guid,
        GetEmployeeListDto,
        CreateUpdateEmployeeDto,
        EmployeeMapper>, IEmployeeAppService
{
    private readonly IEmployeeRepository _employeeRepository;
    private readonly EmployeeManager _employeeManager;

    public EmployeeAppService(
        IEmployeeRepository repository,
        EmployeeManager employeeManager,
        EmployeeMapper employeeMapper) : base(repository, employeeMapper)
    {
        _employeeRepository = repository;
        _employeeManager = employeeManager;

        GetPolicyName = EkbPermissions.Employees.Default;
        GetListPolicyName = EkbPermissions.Employees.Default;
        CreatePolicyName = EkbPermissions.Employees.Create;
        UpdatePolicyName = EkbPermissions.Employees.Edit;
        DeletePolicyName = EkbPermissions.Employees.Delete;
    }

    protected override async Task<IQueryable<Employee>> CreateFilteredQueryAsync(GetEmployeeListDto input)
    {
        return (await _employeeRepository.GetListAsync(
            0,
            int.MaxValue,
            input.Sorting ?? nameof(Employee.FirstName),
            input.Filter,
            input.Department,
            input.IsActive)).AsQueryable();
    }

    public override async Task<PagedResultDto<EmployeeDto>> GetListAsync(GetEmployeeListDto input)
    {
        var totalCount = await _employeeRepository.GetCountAsync(
            input.Filter,
            input.Department,
            input.IsActive);

        var employees = await _employeeRepository.GetListAsync(
            input.SkipCount,
            input.MaxResultCount,
            input.Sorting ?? nameof(Employee.FirstName),
            input.Filter,
            input.Department,
            input.IsActive);

        return new PagedResultDto<EmployeeDto>(
            totalCount,
            MapEntitiesToDtos(employees));
    }

    [Authorize(EkbPermissions.Employees.Create)]
    public override async Task<EmployeeDto> CreateAsync(CreateUpdateEmployeeDto input)
    {
        var employee = await _employeeManager.CreateAsync(
            input.FirstName,
            input.LastName,
            input.Email,
            input.Department,
            input.Position,
            input.HireDate,
            input.Salary,
            input.PhoneNumber,
            input.PhotoPath);

        await _employeeRepository.InsertAsync(employee);
        return MapEntityToDto(employee);
    }

    [Authorize(EkbPermissions.Employees.Edit)]
    public override async Task<EmployeeDto> UpdateAsync(Guid id, CreateUpdateEmployeeDto input)
    {
        var employee = await _employeeRepository.GetAsync(id);

        await _employeeManager.UpdateAsync(
            employee,
            input.FirstName,
            input.LastName,
            input.Email,
            input.Department,
            input.Position,
            input.HireDate,
            input.Salary,
            input.PhoneNumber,
            input.PhotoPath);

        await _employeeRepository.UpdateAsync(employee);
        return MapEntityToDto(employee);
    }

    public async Task<List<string>> GetDepartmentsAsync()
    {
        return await _employeeRepository.GetDepartmentsAsync();
    }

    public async Task<bool> IsEmailUniqueAsync(string email, Guid? excludeId = null)
    {
        return await _employeeRepository.IsEmailUniqueAsync(email, excludeId);
    }

    [Authorize(EkbPermissions.Employees.Delete)]
    public async Task DeleteMultipleAsync(List<Guid> ids)
    {
        await _employeeRepository.DeleteManyAsync(ids);
    }

    [Authorize(EkbPermissions.Employees.Export)]
    public async Task<byte[]> ExportToExcelAsync(GetEmployeeListDto input)
    {
        // Implementation for Excel export would go here
        // For now, return empty byte array
        await Task.CompletedTask;
        return Array.Empty<byte>();
    }

    [Authorize(EkbPermissions.Employees.ManageActivation)]
    public async Task ActivateAsync(Guid id)
    {
        var employee = await _employeeRepository.GetAsync(id);
        employee.Activate();
        await _employeeRepository.UpdateAsync(employee);
    }

    [Authorize(EkbPermissions.Employees.ManageActivation)]
    public async Task DeactivateAsync(Guid id)
    {
        var employee = await _employeeRepository.GetAsync(id);
        employee.Deactivate();
        await _employeeRepository.UpdateAsync(employee);
    }

    #region Custom Mapperly Methods

    // Note: Basic CRUD mapping methods are now handled by the MapperlyCrudAppService base class
    // This eliminates code duplication across all Application Services

    /// <summary>
    /// Example method showing how to use summary DTOs for dashboard views
    /// Uses the GetMapper() helper method from base class to access the mapper
    /// </summary>
    public async Task<List<EmployeeSummaryDto>> GetEmployeeSummariesAsync()
    {
        var employees = await _employeeRepository.GetListAsync(0, int.MaxValue, nameof(Employee.FirstName));
        return GetMapper().ToSummaryDto(employees);
    }

    /// <summary>
    /// Example method showing how to use detailed DTOs with computed properties
    /// Uses the GetMapper() helper method from base class to access the mapper
    /// </summary>
    public async Task<EmployeeDetailDto> GetEmployeeDetailAsync(Guid id)
    {
        var employee = await _employeeRepository.GetAsync(id);
        return GetMapper().ToDetailDto(employee);
    }

    #endregion
}
