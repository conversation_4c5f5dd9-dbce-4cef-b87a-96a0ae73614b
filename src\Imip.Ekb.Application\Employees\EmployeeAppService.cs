using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Imip.Ekb.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Employees;

[Authorize(EkbPermissions.Employees.Default)]
public class EmployeeAppService : CrudAppService<
        Employee,
        EmployeeDto,
        Guid,
        GetEmployeeListDto,
        CreateUpdateEmployeeDto,
        CreateUpdateEmployeeDto>, IEmployeeAppService
{
    private readonly IEmployeeRepository _employeeRepository;
    private readonly EmployeeManager _employeeManager;
    private readonly EmployeeMapper _employeeMapper;

    public EmployeeAppService(
        IEmployeeRepository repository,
        EmployeeManager employeeManager,
        EmployeeMapper employeeMapper) : base(repository)
    {
        _employeeRepository = repository;
        _employeeManager = employeeManager;
        _employeeMapper = employeeMapper;

        GetPolicyName = EkbPermissions.Employees.Default;
        GetListPolicyName = EkbPermissions.Employees.Default;
        CreatePolicyName = EkbPermissions.Employees.Create;
        UpdatePolicyName = EkbPermissions.Employees.Edit;
        DeletePolicyName = EkbPermissions.Employees.Delete;
    }

    protected override async Task<IQueryable<Employee>> CreateFilteredQueryAsync(GetEmployeeListDto input)
    {
        return (await _employeeRepository.GetListAsync(
            0,
            int.MaxValue,
            input.Sorting ?? nameof(Employee.FirstName),
            input.Filter,
            input.Department,
            input.IsActive)).AsQueryable();
    }

    public override async Task<PagedResultDto<EmployeeDto>> GetListAsync(GetEmployeeListDto input)
    {
        var totalCount = await _employeeRepository.GetCountAsync(
            input.Filter,
            input.Department,
            input.IsActive);

        var employees = await _employeeRepository.GetListAsync(
            input.SkipCount,
            input.MaxResultCount,
            input.Sorting ?? nameof(Employee.FirstName),
            input.Filter,
            input.Department,
            input.IsActive);

        return new PagedResultDto<EmployeeDto>(
            totalCount,
            _employeeMapper.ToDto(employees));
    }

    [Authorize(EkbPermissions.Employees.Create)]
    public override async Task<EmployeeDto> CreateAsync(CreateUpdateEmployeeDto input)
    {
        var employee = await _employeeManager.CreateAsync(
            input.FirstName,
            input.LastName,
            input.Email,
            input.Department,
            input.Position,
            input.HireDate,
            input.Salary,
            input.PhoneNumber,
            input.PhotoPath);

        await _employeeRepository.InsertAsync(employee);
        return _employeeMapper.ToDto(employee);
    }

    [Authorize(EkbPermissions.Employees.Edit)]
    public override async Task<EmployeeDto> UpdateAsync(Guid id, CreateUpdateEmployeeDto input)
    {
        var employee = await _employeeRepository.GetAsync(id);

        await _employeeManager.UpdateAsync(
            employee,
            input.FirstName,
            input.LastName,
            input.Email,
            input.Department,
            input.Position,
            input.HireDate,
            input.Salary,
            input.PhoneNumber,
            input.PhotoPath);

        await _employeeRepository.UpdateAsync(employee);
        return _employeeMapper.ToDto(employee);
    }

    public async Task<List<string>> GetDepartmentsAsync()
    {
        return await _employeeRepository.GetDepartmentsAsync();
    }

    public async Task<bool> IsEmailUniqueAsync(string email, Guid? excludeId = null)
    {
        return await _employeeRepository.IsEmailUniqueAsync(email, excludeId);
    }

    [Authorize(EkbPermissions.Employees.Delete)]
    public async Task DeleteMultipleAsync(List<Guid> ids)
    {
        await _employeeRepository.DeleteManyAsync(ids);
    }

    [Authorize(EkbPermissions.Employees.Export)]
    public async Task<byte[]> ExportToExcelAsync(GetEmployeeListDto input)
    {
        // Implementation for Excel export would go here
        // For now, return empty byte array
        await Task.CompletedTask;
        return Array.Empty<byte>();
    }

    [Authorize(EkbPermissions.Employees.ManageActivation)]
    public async Task ActivateAsync(Guid id)
    {
        var employee = await _employeeRepository.GetAsync(id);
        employee.Activate();
        await _employeeRepository.UpdateAsync(employee);
    }

    [Authorize(EkbPermissions.Employees.ManageActivation)]
    public async Task DeactivateAsync(Guid id)
    {
        var employee = await _employeeRepository.GetAsync(id);
        employee.Deactivate();
        await _employeeRepository.UpdateAsync(employee);
    }

    #region Mapperly Override Methods

    /// <summary>
    /// Override MapToGetOutputDto to use Mapperly instead of AutoMapper
    /// </summary>
    protected override EmployeeDto MapToGetOutputDto(Employee entity)
    {
        return _employeeMapper.ToDto(entity);
    }

    /// <summary>
    /// Override MapToGetListOutputDto to use Mapperly instead of AutoMapper
    /// </summary>
    protected override EmployeeDto MapToGetListOutputDto(Employee entity)
    {
        return _employeeMapper.ToDto(entity);
    }

    /// <summary>
    /// Override MapToEntity for Create operations to use Mapperly instead of AutoMapper
    /// </summary>
    protected override Employee MapToEntity(CreateUpdateEmployeeDto createInput)
    {
        return _employeeMapper.ToEntity(createInput);
    }

    /// <summary>
    /// Override MapToEntity for Update operations to use Mapperly instead of AutoMapper
    /// </summary>
    protected override void MapToEntity(CreateUpdateEmployeeDto updateInput, Employee entity)
    {
        _employeeMapper.UpdateEntity(updateInput, entity);
    }

    /// <summary>
    /// Example method showing how to use summary DTOs for dashboard views
    /// </summary>
    public async Task<List<EmployeeSummaryDto>> GetEmployeeSummariesAsync()
    {
        var employees = await _employeeRepository.GetListAsync(0, int.MaxValue, nameof(Employee.FirstName));
        return _employeeMapper.ToSummaryDto(employees);
    }

    /// <summary>
    /// Example method showing how to use detailed DTOs with computed properties
    /// </summary>
    public async Task<EmployeeDetailDto> GetEmployeeDetailAsync(Guid id)
    {
        var employee = await _employeeRepository.GetAsync(id);
        return _employeeMapper.ToDetailDto(employee);
    }

    #endregion
}
