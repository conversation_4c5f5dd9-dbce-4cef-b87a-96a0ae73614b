using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.EntityFrameworkCore;

namespace Imip.Ekb.Services;

/// <summary>
/// Enhanced CRUD Application Service with EF Core Include support and Mapperly mapping
/// Provides configurable eager loading for nested relationships based on operation context
/// </summary>
/// <typeparam name="TEntity">The entity type</typeparam>
/// <typeparam name="TEntityDto">The entity DTO type</typeparam>
/// <typeparam name="TKey">The primary key type</typeparam>
/// <typeparam name="TGetListInput">The input type for GetList operation</typeparam>
/// <typeparam name="TCreateInput">The input type for Create operation</typeparam>
/// <typeparam name="TUpdateInput">The input type for Update operation</typeparam>
/// <typeparam name="TMapper">The Mapperly mapper type</typeparam>
public abstract class EfCoreMapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper> :
    MapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateInput, TUpdateInput, TMapper>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateInput : class
    where TUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateInput, TUpdateInput>
{
    protected readonly IRepository<TEntity, TKey> EfRepository;

    protected EfCoreMapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository, mapper)
    {
        EfRepository = repository;
    }

    #region Include Configuration Methods

    /// <summary>
    /// Configure includes for detail view operations (Get single entity)
    /// Override this method to specify which nested relationships to load for detail views
    /// </summary>
    /// <param name="query">The base query</param>
    /// <returns>Query with includes applied</returns>
    protected virtual IQueryable<TEntity> ApplyDetailIncludes(IQueryable<TEntity> query)
    {
        // Default implementation - no includes
        // Override in derived classes to add specific includes
        return query;
    }

    /// <summary>
    /// Configure includes for list view operations (GetList)
    /// Override this method to specify which nested relationships to load for list views
    /// Should be minimal to avoid performance issues
    /// </summary>
    /// <param name="query">The base query</param>
    /// <returns>Query with includes applied</returns>
    protected virtual IQueryable<TEntity> ApplyListIncludes(IQueryable<TEntity> query)
    {
        // Default implementation - no includes for better performance
        // Override in derived classes if needed, but keep minimal
        return query;
    }

    /// <summary>
    /// Configure includes for edit operations (Update)
    /// Override this method to specify which nested relationships to load for editing
    /// </summary>
    /// <param name="query">The base query</param>
    /// <returns>Query with includes applied</returns>
    protected virtual IQueryable<TEntity> ApplyEditIncludes(IQueryable<TEntity> query)
    {
        // Default implementation uses detail includes
        return ApplyDetailIncludes(query);
    }

    /// <summary>
    /// Configure includes based on operation context
    /// </summary>
    /// <param name="query">The base query</param>
    /// <param name="context">The operation context</param>
    /// <returns>Query with context-appropriate includes</returns>
    protected virtual IQueryable<TEntity> ApplyIncludesForContext(IQueryable<TEntity> query, string context)
    {
        return context switch
        {
            "detail" => ApplyDetailIncludes(query),
            "list" => ApplyListIncludes(query),
            "edit" => ApplyEditIncludes(query),
            "create" => query, // No includes needed for create
            _ => query
        };
    }

    #endregion

    #region Enhanced CRUD Operations with EF Core Includes

    /// <summary>
    /// Gets a single entity with detail includes applied
    /// </summary>
    public override async Task<TEntityDto> GetAsync(TKey id)
    {
        var query = await EfRepository.GetQueryableAsync();
        query = ApplyDetailIncludes(query);

        var entity = await AsyncExecuter.FirstOrDefaultAsync(query.Where(e => e.Id.Equals(id)));
        if (entity == null)
        {
            throw new EntityNotFoundException(typeof(TEntity), id);
        }

        return MapToGetOutputDto(entity);
    }

    /// <summary>
    /// Gets entities with list includes applied for optimal performance
    /// </summary>
    public override async Task<PagedResultDto<TEntityDto>> GetListAsync(TGetListInput input)
    {
        var query = await CreateFilteredQueryAsync(input);

        // Apply list-specific includes (should be minimal)
        query = ApplyListIncludes(query);

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = ApplySorting(query, input);
        query = ApplyPaging(query, input);

        var entities = await AsyncExecuter.ToListAsync(query);
        var dtos = MapEntitiesToDtos(entities);

        return new PagedResultDto<TEntityDto>(totalCount, dtos);
    }

    /// <summary>
    /// Updates entity with edit includes applied
    /// </summary>
    public override async Task<TEntityDto> UpdateAsync(TKey id, TUpdateInput input)
    {
        var query = await EfRepository.GetQueryableAsync();
        query = ApplyEditIncludes(query);

        var entity = await AsyncExecuter.FirstOrDefaultAsync(query.Where(e => e.Id.Equals(id)));
        if (entity == null)
        {
            throw new EntityNotFoundException(typeof(TEntity), id);
        }

        MapToEntity(input, entity);
        await EfRepository.UpdateAsync(entity);

        return MapToGetOutputDto(entity);
    }

    #endregion

    #region Context-Based Operations

    /// <summary>
    /// Gets entity with includes based on specified context
    /// </summary>
    /// <param name="id">Entity ID</param>
    /// <param name="context">Operation context (detail, edit, etc.)</param>
    /// <returns>Entity DTO with appropriate nested data</returns>
    public virtual async Task<TEntityDto> GetWithContextAsync(TKey id, string context = "detail")
    {
        var query = await EfRepository.GetQueryableAsync();
        query = ApplyIncludesForContext(query, context);

        var entity = await AsyncExecuter.FirstOrDefaultAsync(query.Where(e => e.Id.Equals(id)));
        if (entity == null)
        {
            throw new EntityNotFoundException(typeof(TEntity), id);
        }

        return MapToGetOutputDto(entity);
    }

    /// <summary>
    /// Gets entities with includes based on specified context
    /// </summary>
    /// <param name="input">List input parameters</param>
    /// <param name="context">Operation context</param>
    /// <returns>Paged result with appropriate nested data</returns>
    public virtual async Task<PagedResultDto<TEntityDto>> GetListWithContextAsync(TGetListInput input, string context = "list")
    {
        var query = await CreateFilteredQueryAsync(input);
        query = ApplyIncludesForContext(query, context);

        var totalCount = await AsyncExecuter.CountAsync(query);

        query = ApplySorting(query, input);
        query = ApplyPaging(query, input);

        var entities = await AsyncExecuter.ToListAsync(query);
        var dtos = MapEntitiesToDtos(entities);

        return new PagedResultDto<TEntityDto>(totalCount, dtos);
    }

    #endregion

    #region Helper Methods for Include Management

    /// <summary>
    /// Helper method to safely add includes to a query
    /// Prevents duplicate includes and provides better error handling
    /// </summary>
    protected virtual IQueryable<TEntity> SafeInclude<TProperty>(
        IQueryable<TEntity> query,
        Expression<Func<TEntity, TProperty>> navigationPropertyPath)
    {
        try
        {
            return query.Include(navigationPropertyPath);
        }
        catch (Exception ex)
        {
            // Log the error and return original query
            Logger.LogWarning(ex, "Failed to apply include for {PropertyPath}", navigationPropertyPath);
            return query;
        }
    }

    /// <summary>
    /// Helper method to safely add ThenInclude to a query
    /// </summary>
    protected virtual IQueryable<TEntity> SafeThenInclude<TPreviousProperty, TProperty>(
        IIncludableQueryable<TEntity, TPreviousProperty> query,
        Expression<Func<TPreviousProperty, TProperty>> navigationPropertyPath)
    {
        try
        {
            return query.ThenInclude(navigationPropertyPath);
        }
        catch (Exception ex)
        {
            // Log the error and return original query
            Logger.LogWarning(ex, "Failed to apply ThenInclude for {PropertyPath}", navigationPropertyPath);
            return query;
        }
    }

    /// <summary>
    /// Helper method to check if an entity has a specific navigation property loaded
    /// </summary>
    protected virtual bool IsNavigationPropertyLoaded<TProperty>(TEntity entity, Expression<Func<TEntity, TProperty>> navigationPropertyPath)
    {
        if (EfRepository is IEfCoreRepository<TEntity, TKey> efRepository)
        {
            var dbContext = efRepository.GetDbContext();
            var entry = dbContext.Entry(entity);

            if (navigationPropertyPath.Body is MemberExpression memberExpression)
            {
                var propertyName = memberExpression.Member.Name;
                return entry.Navigation(propertyName).IsLoaded;
            }
        }

        return false;
    }

    #endregion

    #region Performance Monitoring

    /// <summary>
    /// Override this method to add performance monitoring for include operations
    /// </summary>
    protected virtual void LogIncludePerformance(string operation, int entityCount, TimeSpan duration)
    {
        if (duration.TotalMilliseconds > 1000) // Log slow queries
        {
            Logger.LogWarning(
                "Slow query detected in {Operation}: {EntityCount} entities loaded in {Duration}ms",
                operation, entityCount, duration.TotalMilliseconds);
        }
    }

    #endregion
}

/// <summary>
/// Simplified EF Core version for cases where Create and Update DTOs are the same
/// </summary>
public abstract class EfCoreMapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TMapper> :
    EfCoreMapperlyCrudAppService<TEntity, TEntityDto, TKey, TGetListInput, TCreateUpdateInput, TCreateUpdateInput, TMapper>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TGetListInput : IPagedAndSortedResultRequest
    where TCreateUpdateInput : class
    where TMapper : IAbpMapper<TEntity, TEntityDto, TKey, TCreateUpdateInput, TCreateUpdateInput>
{
    protected EfCoreMapperlyCrudAppService(
        IRepository<TEntity, TKey> repository,
        TMapper mapper) : base(repository, mapper)
    {
    }
}
