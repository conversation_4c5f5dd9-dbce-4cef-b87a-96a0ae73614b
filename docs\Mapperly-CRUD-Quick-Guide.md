# Quick Guide: Creating CRUD with <PERSON><PERSON><PERSON> in ABP Framework

This is a step-by-step checklist for creating CRUD operations using <PERSON><PERSON>ly instead of AutoMapper in ABP Framework.

## Prerequisites Checklist

- [ ] ABP Framework project setup
- [ ] Riok.Mapperly NuGet package installed in Application layer
- [ ] Mapperly infrastructure created (IAbpMapper interface, extensions)
- [ ] Mappers registered in Application Module

## Step-by-Step CRUD Creation

### 1. Create the Domain Entity

```csharp
// src/YourProject.Domain/YourEntities/YourEntity.cs
[SetsRequiredMembers]
public class YourEntity : AuditedAggregateRoot<Guid>
{
    public required string Name { get; set; }
    public required string Description { get; set; }
    // Add other properties...

    protected YourEntity() { }

    [SetsRequiredMembers]
    public YourEntity(Guid id, string name, string description) : base(id)
    {
        Name = name;
        Description = description;
    }
}
```

**Key Points:**
- Use `[SetsRequiredMembers]` on constructor for required properties
- Provide protected parameterless constructor for EF Core
- Follow ABP entity conventions (inherit from appropriate base class)

### 2. Create DTOs

```csharp
// src/YourProject.Application.Contracts/YourEntities/YourEntityDto.cs
public class YourEntityDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    // Add other properties...
}

// src/YourProject.Application.Contracts/YourEntities/CreateUpdateYourEntityDto.cs
public class CreateUpdateYourEntityDto
{
    [Required]
    [StringLength(128, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;

    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;
    // Add other properties with validation attributes...
}

// src/YourProject.Application.Contracts/YourEntities/GetYourEntityListDto.cs
public class GetYourEntityListDto : PagedAndSortedResultRequestDto
{
    public string? Filter { get; set; }
    // Add other filter properties...
}
```

**Key Points:**
- Initialize string properties with `= string.Empty` to avoid nullable warnings
- Use appropriate validation attributes
- Follow ABP DTO naming conventions

### 3. Create Mapperly Mapper

```csharp
// src/YourProject.Application/Mapping/YourEntityMapper.cs
[Mapper]
public partial class YourEntityMapper : IAbpMapper<YourEntity, YourEntityDto, Guid, CreateUpdateYourEntityDto>
{
    /// <summary>
    /// Maps Entity to DTO
    /// </summary>
    [MapperIgnoreSource(nameof(YourEntity.ExtraProperties))]
    [MapperIgnoreSource(nameof(YourEntity.ConcurrencyStamp))]
    public partial YourEntityDto ToDto(YourEntity entity);

    /// <summary>
    /// Maps list of entities to DTOs
    /// </summary>
    public partial List<YourEntityDto> ToDto(List<YourEntity> entities);

    /// <summary>
    /// Creates new entity from DTO
    /// </summary>
    public YourEntity ToEntity(CreateUpdateYourEntityDto createDto)
    {
        return new YourEntity(
            Guid.NewGuid(),
            createDto.Name,
            createDto.Description
            // Map other properties...
        );
    }

    /// <summary>
    /// Creates new entity with specified ID
    /// </summary>
    public YourEntity ToEntity(CreateUpdateYourEntityDto createDto, Guid id)
    {
        return new YourEntity(
            id,
            createDto.Name,
            createDto.Description
            // Map other properties...
        );
    }

    /// <summary>
    /// Updates existing entity
    /// </summary>
    public void UpdateEntity(CreateUpdateYourEntityDto updateDto, YourEntity entity)
    {
        entity.Name = updateDto.Name;
        entity.Description = updateDto.Description;
        // Update other properties...
    }
}
```

**Key Points:**
- Use `[Mapper]` attribute on the class
- Inherit from `IAbpMapper<>` interface
- Use `partial` keyword for auto-generated methods
- Ignore ABP system properties (ExtraProperties, ConcurrencyStamp)
- Use entity constructor for creating new entities
- Manual property assignment for updates

### 4. Register Mapper in DI Container

```csharp
// src/YourProject.Application/YourProjectApplicationModule.cs
private void ConfigureMapperlyMappers(ServiceConfigurationContext context)
{
    // Register mapper as singleton
    context.Services.AddSingleton<YourEntityMapper>();
    
    // Register as interface (optional, for dependency injection)
    context.Services.AddSingleton<IAbpMapper<YourEntity, YourEntityDto, Guid, CreateUpdateYourEntityDto>>(
        provider => provider.GetRequiredService<YourEntityMapper>());
}
```

**Key Points:**
- Register as singleton for better performance
- Call this method from `ConfigureServices`

### 5. Create Application Service

```csharp
// src/YourProject.Application/YourEntities/YourEntityAppService.cs
[Authorize(YourProjectPermissions.YourEntities.Default)]
public class YourEntityAppService :
    MapperlyCrudAppService<
        YourEntity,                  // The entity
        YourEntityDto,               // Used to show entities
        Guid,                        // Primary key type
        GetYourEntityListDto,        // Used for paging/sorting/filtering
        CreateUpdateYourEntityDto,   // Used to create/update entity
        YourEntityMapper>,           // The Mapperly mapper
    IYourEntityAppService
{
    public YourEntityAppService(
        IRepository<YourEntity, Guid> repository,
        YourEntityMapper mapper) : base(repository, mapper)
    {
        // Configure permissions
        GetPolicyName = YourProjectPermissions.YourEntities.Default;
        GetListPolicyName = YourProjectPermissions.YourEntities.Default;
        CreatePolicyName = YourProjectPermissions.YourEntities.Create;
        UpdatePolicyName = YourProjectPermissions.YourEntities.Edit;
        DeletePolicyName = YourProjectPermissions.YourEntities.Delete;
    }

    // No need for override methods! They're handled by the base class.
    // Use GetMapper() for custom scenarios:

    public async Task<YourEntityDto> GetEnhancedEntityAsync(Guid id)
    {
        var entity = await Repository.GetAsync(id);
        return GetMapper().ToDto(entity); // Access mapper via base class
    }
}
```

**Key Points:**
- Inherit from `MapperlyCrudAppService<>` instead of `CrudAppService<>`
- Pass the mapper to the base constructor
- No need to override mapping methods - they're handled automatically by the base class
- Use `GetMapper()` helper method for custom scenarios
- Keep existing ABP conventions (permissions, interfaces)

### 6. Create Application Service Interface

```csharp
// src/YourProject.Application.Contracts/YourEntities/IYourEntityAppService.cs
public interface IYourEntityAppService : ICrudAppService<
    YourEntityDto,
    Guid,
    GetYourEntityListDto,
    CreateUpdateYourEntityDto,
    CreateUpdateYourEntityDto>
{
    // Add custom methods if needed
}
```

### 7. Build and Test

```bash
dotnet build
```

**Common Build Issues:**
- Required member errors → Add `[SetsRequiredMembers]` to constructors
- Nullable warnings → Initialize string properties with default values
- Mapping errors → Check property names and types match

## Advanced Scenarios

### Complex Mappings with Business Logic

```csharp
public YourEntityDetailDto ToDetailDto(YourEntity entity)
{
    var dto = new YourEntityDetailDto
    {
        Id = entity.Id,
        Name = entity.Name,
        Description = entity.Description,
        
        // Custom computed properties
        DisplayName = $"{entity.Name} ({entity.Code})",
        FormattedDate = entity.CreationTime.ToString("yyyy-MM-dd"),
        Status = entity.IsActive ? "Active" : "Inactive"
    };

    // Map auditing properties if needed
    AbpMapperExtensions.MapAuditingProperties(entity, dto);

    return dto;
}
```

### Handling Nullable Properties

```csharp
// In DTO
public string? Position { get; set; }  // Nullable

// In Entity  
public string? Position { get; set; }  // Also nullable

// Mapping works automatically for matching nullability
```

## Testing Your Mapper

```csharp
public class YourEntityMapperTests : YourProjectTestBase
{
    private readonly YourEntityMapper _mapper;

    public YourEntityMapperTests()
    {
        _mapper = new YourEntityMapper();
    }

    [Fact]
    public void Should_Map_Entity_To_Dto()
    {
        // Arrange
        var entity = new YourEntity(Guid.NewGuid(), "Test Name", "Test Description");

        // Act
        var dto = _mapper.ToDto(entity);

        // Assert
        dto.Id.ShouldBe(entity.Id);
        dto.Name.ShouldBe(entity.Name);
        dto.Description.ShouldBe(entity.Description);
    }

    [Fact]
    public void Should_Create_Entity_From_Dto()
    {
        // Arrange
        var createDto = new CreateUpdateYourEntityDto
        {
            Name = "Test Name",
            Description = "Test Description"
        };

        // Act
        var entity = _mapper.ToEntity(createDto);

        // Assert
        entity.Name.ShouldBe(createDto.Name);
        entity.Description.ShouldBe(createDto.Description);
    }
}
```

## Checklist Summary

- [ ] Entity created with `[SetsRequiredMembers]` constructor
- [ ] DTOs created with proper validation and default values
- [ ] Mapper created with `[Mapper]` attribute and proper mappings
- [ ] Mapper registered in DI container as singleton
- [ ] Application service inherits from `MapperlyCrudAppService<>` base class
- [ ] Mapper passed to base constructor
- [ ] Application service interface created
- [ ] Project builds successfully
- [ ] Tests written and passing

## Key Advantage: Zero Code Duplication

The `MapperlyCrudAppService<>` base class eliminates the need to override mapping methods in every Application Service. This provides:

- **No duplicate code**: Override methods are implemented once in the base class
- **Easy adoption**: Just change the inheritance and constructor
- **Consistent behavior**: All services use the same mapping logic
- **Maintainable**: Changes to mapping logic only need to be made in one place

## Performance Benefits

Using Mapperly instead of AutoMapper provides:
- **~10-50x faster mapping** (compile-time vs runtime)
- **No reflection overhead** at runtime
- **Better memory usage** (no expression trees)
- **AOT compatibility** for Native AOT scenarios
- **Type safety** at compile time
