using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Attachments;

/// <summary>
/// DTO for bulk file download requests
/// </summary>
public class BulkDownloadDto
{
    /// <summary>
    /// List of file IDs to download
    /// </summary>
    [Required]
    public List<Guid>? FileIds { get; set; }

    /// <summary>
    /// Optional custom name for the zip file (without extension)
    /// </summary>
    [StringLength(100)]
    public string? ZipFileName { get; set; }
}