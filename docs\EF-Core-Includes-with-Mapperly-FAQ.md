# EF Core Includes with <PERSON><PERSON><PERSON> in ABP Framework - FAQ

This document answers specific questions about integrating EF Core's `.Include()` and `.ThenInclude()` methods with Mapperly in ABP Framework Application Services.

## Q1: How does the MapperlyCrudAppService handle EF Core's eager loading with .Include() and .ThenInclude()?

### Answer:

The **standard `MapperlyCrudAppService`** does NOT automatically handle EF Core includes. It inherits from ABP's `CrudAppService` which uses basic repository queries without eager loading.

**However**, the **enhanced `EfCoreMapperlyCrudAppService`** provides full support for EF Core includes:

```csharp
// Standard version - NO include support
public class ProductAppService : MapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>
{
    // Uses basic repository queries - no includes
}

// Enhanced version - FULL include support
public class ProductAppService : EfCoreMapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>
{
    // Provides configurable include support for different contexts
    protected override IQueryable<Product> ApplyDetailIncludes(IQueryable<Product> query)
    {
        return query
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.Reviews)
                .ThenInclude(r => r.Customer);
    }
}
```

## Q2: Can I override the query building methods in the base class to add .Include() statements?

### Answer:

**Yes!** The `EfCoreMapperlyCrudAppService` provides several virtual methods specifically for this purpose:

```csharp
public class OrderAppService : EfCoreMapperlyCrudAppService<Order, OrderDto, Guid, GetOrderListDto, CreateUpdateOrderDto, OrderMapper>
{
    // Override for detail views (Get single entity)
    protected override IQueryable<Order> ApplyDetailIncludes(IQueryable<Order> query)
    {
        return query
            .Include(o => o.Customer)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
            .Include(o => o.ShippingAddress);
    }

    // Override for list views (GetList operations)
    protected override IQueryable<Order> ApplyListIncludes(IQueryable<Order> query)
    {
        return query
            .Include(o => o.Customer); // Minimal includes for performance
    }

    // Override for edit operations
    protected override IQueryable<Order> ApplyEditIncludes(IQueryable<Order> query)
    {
        return query
            .Include(o => o.Customer)
            .Include(o => o.OrderItems); // Include items but not products for editing
    }

    // Override for custom context-based includes
    protected override IQueryable<Order> ApplyIncludesForContext(IQueryable<Order> query, string context)
    {
        return context switch
        {
            "detail" => ApplyDetailIncludes(query),
            "list" => ApplyListIncludes(query),
            "edit" => ApplyEditIncludes(query),
            "summary" => query.Include(o => o.Customer), // Custom context
            _ => query
        };
    }
}
```

## Q3: Does the EnhancedMapperlyCrudAppService provide built-in support for configuring nested relationships based on operation context?

### Answer:

**Yes!** The `EfCoreMapperlyCrudAppService` provides context-aware include configuration:

```csharp
public class CustomerAppService : EfCoreMapperlyCrudAppService<Customer, CustomerDto, Guid, GetCustomerListDto, CreateUpdateCustomerDto, CustomerMapper>
{
    // Built-in context-aware methods
    public async Task<CustomerDto> GetCustomerForEditAsync(Guid id)
    {
        return await GetWithContextAsync(id, "edit"); // Uses ApplyEditIncludes
    }

    public async Task<CustomerDto> GetCustomerDetailAsync(Guid id)
    {
        return await GetWithContextAsync(id, "detail"); // Uses ApplyDetailIncludes
    }

    public async Task<PagedResultDto<CustomerDto>> GetCustomerListOptimizedAsync(GetCustomerListDto input)
    {
        return await GetListWithContextAsync(input, "list"); // Uses ApplyListIncludes
    }

    // Configure different include strategies per context
    protected override IQueryable<Customer> ApplyDetailIncludes(IQueryable<Customer> query)
    {
        return query
            .Include(c => c.Orders)
                .ThenInclude(o => o.OrderItems)
            .Include(c => c.DefaultAddress);
    }

    protected override IQueryable<Customer> ApplyListIncludes(IQueryable<Customer> query)
    {
        return query; // No includes for list performance
    }

    protected override IQueryable<Customer> ApplyEditIncludes(IQueryable<Customer> query)
    {
        return query
            .Include(c => c.Orders) // Orders but not items
            .Include(c => c.DefaultAddress);
    }
}
```

## Q4: What is the recommended approach for combining EF Core's .Include() with Mapperly's mapping capabilities?

### Answer:

**Recommended Pattern:**

1. **Use `EfCoreMapperlyCrudAppService` as base class**
2. **Configure context-specific includes**
3. **Let Mapperly handle the mapping automatically**
4. **Use projections for read-only scenarios**

```csharp
// Step 1: Use the enhanced base class
public class ProductAppService : EfCoreMapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>
{
    // Step 2: Configure includes per context
    protected override IQueryable<Product> ApplyDetailIncludes(IQueryable<Product> query)
    {
        return query
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.ProductAttributes)
                .ThenInclude(pa => pa.AttributeValue);
    }

    protected override IQueryable<Product> ApplyListIncludes(IQueryable<Product> query)
    {
        return query
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1)); // Only first image
    }

    // Step 3: Mapperly handles mapping automatically via base class
    // No manual mapping code needed!

    // Step 4: Use projections for read-only scenarios
    public async Task<PagedResultDto<ProductSummaryDto>> GetProductSummariesAsync(GetProductListDto input)
    {
        var query = await EfRepository.GetQueryableAsync();
        
        // Direct projection - no includes needed
        var projectionQuery = query
            .Select(p => new ProductSummaryDto
            {
                Id = p.Id,
                Name = p.Name,
                Price = p.Price,
                CategoryName = p.Category.Name,
                ImageUrl = p.ProductImages.FirstOrDefault().Url
            });

        var totalCount = await AsyncExecuter.CountAsync(projectionQuery);
        projectionQuery = projectionQuery.Skip(input.SkipCount).Take(input.MaxResultCount);

        var dtos = await AsyncExecuter.ToListAsync(projectionQuery);
        return new PagedResultDto<ProductSummaryDto>(totalCount, dtos);
    }
}
```

## Q5: Are there performance considerations and best practices for avoiding N+1 query problems?

### Answer:

**Yes! Here are the key performance considerations:**

### ✅ **Best Practices:**

1. **Context-Specific Includes**:
```csharp
// ✅ GOOD: Different includes for different contexts
protected override IQueryable<Order> ApplyListIncludes(IQueryable<Order> query)
{
    return query.Include(o => o.Customer); // Minimal for lists
}

protected override IQueryable<Order> ApplyDetailIncludes(IQueryable<Order> query)
{
    return query
        .Include(o => o.Customer)
        .Include(o => o.OrderItems)
            .ThenInclude(oi => oi.Product); // Full data for details
}
```

2. **Limit Collection Sizes**:
```csharp
// ✅ GOOD: Limit large collections
protected override IQueryable<Product> ApplyDetailIncludes(IQueryable<Product> query)
{
    return query
        .Include(p => p.Reviews.Take(10)) // Limit to 10 reviews
        .Include(p => p.ProductImages.Take(5)); // Limit to 5 images
}
```

3. **Use Projections for Read-Only Data**:
```csharp
// ✅ GOOD: Projection instead of includes
public async Task<List<OrderSummaryDto>> GetOrderSummariesAsync()
{
    var query = await EfRepository.GetQueryableAsync();
    
    return await AsyncExecuter.ToListAsync(
        query.Select(o => new OrderSummaryDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            CustomerName = o.Customer.Name, // No include needed
            TotalAmount = o.OrderItems.Sum(oi => oi.Quantity * oi.UnitPrice)
        }));
}
```

4. **Monitor Performance**:
```csharp
// ✅ GOOD: Performance monitoring
protected override void LogIncludePerformance(string operation, int entityCount, TimeSpan duration)
{
    base.LogIncludePerformance(operation, entityCount, duration);
    
    if (entityCount > 100 && duration.TotalMilliseconds > 500)
    {
        Logger.LogWarning(
            "Large dataset query in {Operation}: Consider optimization. " +
            "Entities: {EntityCount}, Duration: {Duration}ms",
            operation, entityCount, duration.TotalMilliseconds);
    }
}
```

### ❌ **Anti-Patterns to Avoid:**

1. **Loading Everything Always**:
```csharp
// ❌ BAD: Too many includes for list views
protected override IQueryable<Customer> ApplyListIncludes(IQueryable<Customer> query)
{
    return query
        .Include(c => c.Orders)
            .ThenInclude(o => o.OrderItems)
                .ThenInclude(oi => oi.Product); // Too much for a list!
}
```

2. **Deep Nested Includes Without Limits**:
```csharp
// ❌ BAD: Unlimited deep includes
protected override IQueryable<Category> ApplyDetailIncludes(IQueryable<Category> query)
{
    return query
        .Include(c => c.SubCategories)
            .ThenInclude(sc => sc.SubCategories)
                .ThenInclude(ssc => sc.SubCategories); // Potential infinite recursion!
}
```

3. **Not Using Projections for Large Datasets**:
```csharp
// ❌ BAD: Loading full entities when only summary needed
public async Task<List<ProductDto>> GetAllProductsAsync()
{
    var query = await EfRepository.GetQueryableAsync();
    var products = await AsyncExecuter.ToListAsync(
        query
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.Reviews)); // Loading everything!
    
    return MapEntitiesToDtos(products);
}
```

### 🚀 **Performance Optimization Summary:**

| Scenario | Recommended Approach | Avoid |
|----------|---------------------|-------|
| **List Views** | Minimal includes or projections | Deep nested includes |
| **Detail Views** | Context-specific includes with limits | Loading all related data |
| **Search Results** | Projections with only needed fields | Full entity loading |
| **Large Collections** | Use `.Take()` to limit size | Unlimited collection loading |
| **Hierarchical Data** | Limit depth (max 3-5 levels) | Infinite recursion |
| **Read-Only Data** | Use projections | Entity tracking |

The `EfCoreMapperlyCrudAppService` provides the infrastructure to implement these patterns effectively while maintaining the benefits of Mapperly's compile-time mapping.
