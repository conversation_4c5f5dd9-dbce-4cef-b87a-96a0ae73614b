# EF Core Includes with <PERSON><PERSON><PERSON> in ABP Framework - FAQ

This document answers specific questions about integrating EF Core's `.Include()` and `.ThenInclude()` methods with <PERSON>perly in ABP Framework Application Services while following proper ABP architectural principles.

## Q1: How does the MapperlyCrudAppService handle EF Core's eager loading with .Include() and .ThenInclude()?

### Answer:

The **`MapperlyCrudAppService`** follows ABP Framework's architectural principles and does NOT directly handle EF Core includes in the Application layer. This is by design and follows ABP's layered architecture where:

- **Application Layer**: Contains business logic and uses repository abstractions
- **EntityFrameworkCore Layer**: Contains EF Core-specific implementations including includes

**Correct ABP Approach for Nested Relationships:**

### Option 1: Custom Repository Methods (Recommended)

Create custom repository methods in the EntityFrameworkCore layer:

```csharp
// In EntityFrameworkCore layer: src/YourProject.EntityFrameworkCore/Products/EfCoreProductRepository.cs
public class EfCoreProductRepository : EfCoreRepository<YourProjectDbContext, Product, Guid>, IProductRepository
{
    public EfCoreProductRepository(IDbContextProvider<YourProjectDbContext> dbContextProvider)
        : base(dbContextProvider)
    {
    }

    public async Task<Product> GetWithDetailsAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.Reviews)
                .ThenInclude(r => r.Customer)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<List<Product>> GetListWithBasicIncludesAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1)) // Only first image for performance
            .ToListAsync();
    }
}

// In Domain layer: src/YourProject.Domain/Products/IProductRepository.cs
public interface IProductRepository : IRepository<Product, Guid>
{
    Task<Product> GetWithDetailsAsync(Guid id);
    Task<List<Product>> GetListWithBasicIncludesAsync();
}

// In Application layer: Use the custom repository methods
public class ProductAppService : MapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>
{
    private readonly IProductRepository _productRepository;

    public ProductAppService(IProductRepository repository, ProductMapper mapper) : base(repository, mapper)
    {
        _productRepository = repository;
    }

    // Override to use custom repository method with includes
    public override async Task<ProductDto> GetAsync(Guid id)
    {
        var product = await _productRepository.GetWithDetailsAsync(id);
        return MapToGetOutputDto(product);
    }

    // Custom method for list with optimized includes
    public async Task<List<ProductDto>> GetListWithImagesAsync()
    {
        var products = await _productRepository.GetListWithBasicIncludesAsync();
        return MapEntitiesToDtos(products);
    }
}
```

## Q2: Can I override the query building methods in the base class to add .Include() statements?

### Answer:

**No, you should NOT add EF Core includes directly in the Application layer.** This violates ABP Framework's architectural principles. Instead, use these ABP-compliant approaches:

### Option 2: Using ABP's includeDetails Parameter

ABP's repository methods support an `includeDetails` parameter:

```csharp
public class OrderAppService : MapperlyCrudAppService<Order, OrderDto, Guid, GetOrderListDto, CreateUpdateOrderDto, OrderMapper>
{
    private readonly IOrderRepository _orderRepository;

    public OrderAppService(IOrderRepository repository, OrderMapper mapper) : base(repository, mapper)
    {
        _orderRepository = repository;
    }

    // Override to use repository method with includes for detail view
    public override async Task<OrderDto> GetAsync(Guid id)
    {
        var order = await Repository.GetAsync(id, includeDetails: true);
        return MapToGetOutputDto(order);
    }

    // Custom method using specific repository method
    public async Task<OrderDto> GetOrderWithDetailsAsync(Guid id)
    {
        var order = await _orderRepository.GetWithDetailsAsync(id);
        return MapToGetOutputDto(order);
    }

    // Custom method for list with minimal includes
    public async Task<List<OrderDto>> GetOrderListWithCustomerAsync()
    {
        var orders = await _orderRepository.GetListWithCustomerAsync();
        return MapEntitiesToDtos(orders);
    }
}

// Repository interface in Domain layer
public interface IOrderRepository : IRepository<Order, Guid>
{
    Task<Order> GetWithDetailsAsync(Guid id);
    Task<List<Order>> GetListWithCustomerAsync();
}

// Repository implementation in EntityFrameworkCore layer
public class EfCoreOrderRepository : EfCoreRepository<YourDbContext, Order, Guid>, IOrderRepository
{
    public async Task<Order> GetWithDetailsAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(o => o.Customer)
            .Include(o => o.OrderItems)
                .ThenInclude(oi => oi.Product)
            .Include(o => o.ShippingAddress)
            .FirstOrDefaultAsync(o => o.Id == id);
    }

    public async Task<List<Order>> GetListWithCustomerAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(o => o.Customer)
            .ToListAsync();
    }
}
```

## Q3: Does the MapperlyCrudAppService provide built-in support for configuring nested relationships based on operation context?

### Answer:

**The `MapperlyCrudAppService` follows ABP principles and delegates include logic to the repository layer.** Context-aware loading is achieved through custom repository methods:

```csharp
// Repository interface with context-specific methods
public interface ICustomerRepository : IRepository<Customer, Guid>
{
    Task<Customer> GetForEditAsync(Guid id);
    Task<Customer> GetWithFullDetailsAsync(Guid id);
    Task<List<Customer>> GetListOptimizedAsync();
}

// Repository implementation in EntityFrameworkCore layer
public class EfCoreCustomerRepository : EfCoreRepository<YourDbContext, Customer, Guid>, ICustomerRepository
{
    public async Task<Customer> GetForEditAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(c => c.Orders) // Orders but not items for editing
            .Include(c => c.DefaultAddress)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<Customer> GetWithFullDetailsAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(c => c.Orders)
                .ThenInclude(o => o.OrderItems)
            .Include(c => c.DefaultAddress)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    public async Task<List<Customer>> GetListOptimizedAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet.ToListAsync(); // No includes for performance
    }
}

// Application Service using context-specific repository methods
public class CustomerAppService : MapperlyCrudAppService<Customer, CustomerDto, Guid, GetCustomerListDto, CreateUpdateCustomerDto, CustomerMapper>
{
    private readonly ICustomerRepository _customerRepository;

    public CustomerAppService(ICustomerRepository repository, CustomerMapper mapper) : base(repository, mapper)
    {
        _customerRepository = repository;
    }

    // Context-specific methods
    public async Task<CustomerDto> GetCustomerForEditAsync(Guid id)
    {
        var customer = await _customerRepository.GetForEditAsync(id);
        return MapToGetOutputDto(customer);
    }

    public async Task<CustomerDto> GetCustomerWithFullDetailsAsync(Guid id)
    {
        var customer = await _customerRepository.GetWithFullDetailsAsync(id);
        return MapToGetOutputDto(customer);
    }

    public async Task<List<CustomerDto>> GetCustomerListOptimizedAsync()
    {
        var customers = await _customerRepository.GetListOptimizedAsync();
        return MapEntitiesToDtos(customers);
    }
}
```

## Q4: What is the recommended approach for combining EF Core's .Include() with Mapperly's mapping capabilities?

### Answer:

**ABP-Compliant Recommended Pattern:**

1. **Use `MapperlyCrudAppService` as base class** (follows ABP architecture)
2. **Implement EF Core includes in custom repository methods** (EntityFrameworkCore layer)
3. **Create context-specific repository methods** (for different scenarios)
4. **Let Mapperly handle the mapping automatically** (Application layer)
5. **Use projections for read-only scenarios** (when appropriate)

```csharp
// Step 1: Create custom repository interface (Domain layer)
public interface IProductRepository : IRepository<Product, Guid>
{
    Task<Product> GetWithDetailsAsync(Guid id);
    Task<List<Product>> GetListWithBasicIncludesAsync();
}

// Step 2: Implement repository with EF Core includes (EntityFrameworkCore layer)
public class EfCoreProductRepository : EfCoreRepository<YourDbContext, Product, Guid>, IProductRepository
{
    public async Task<Product> GetWithDetailsAsync(Guid id)
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.ProductAttributes)
                .ThenInclude(pa => pa.AttributeValue)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    public async Task<List<Product>> GetListWithBasicIncludesAsync()
    {
        var dbSet = await GetDbSetAsync();
        return await dbSet
            .Include(p => p.Category)
            .Include(p => p.ProductImages.Take(1)) // Only first image
            .ToListAsync();
    }
}

// Step 3: Use MapperlyCrudAppService with custom repository (Application layer)
public class ProductAppService : MapperlyCrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, ProductMapper>
{
    private readonly IProductRepository _productRepository;

    public ProductAppService(IProductRepository repository, ProductMapper mapper) : base(repository, mapper)
    {
        _productRepository = repository;
    }

    // Override to use custom repository method with includes
    public override async Task<ProductDto> GetAsync(Guid id)
    {
        var product = await _productRepository.GetWithDetailsAsync(id);
        return MapToGetOutputDto(product); // Mapperly handles mapping automatically
    }

    // Custom method for list with optimized includes
    public async Task<List<ProductDto>> GetListWithImagesAsync()
    {
        var products = await _productRepository.GetListWithBasicIncludesAsync();
        return MapEntitiesToDtos(products); // Mapperly handles mapping automatically
    }

    // Step 4: Use projections for read-only scenarios
    public async Task<PagedResultDto<ProductSummaryDto>> GetProductSummariesAsync(GetProductListDto input)
    {
        var query = await Repository.GetQueryableAsync();

        // Direct projection - no includes needed, better performance
        var projectionQuery = query
            .Select(p => new ProductSummaryDto
            {
                Id = p.Id,
                Name = p.Name,
                Price = p.Price,
                CategoryName = p.Category.Name,
                ImageUrl = p.ProductImages.FirstOrDefault().Url
            });

        var totalCount = await AsyncExecuter.CountAsync(projectionQuery);
        projectionQuery = projectionQuery.Skip(input.SkipCount).Take(input.MaxResultCount);

        var dtos = await AsyncExecuter.ToListAsync(projectionQuery);
        return new PagedResultDto<ProductSummaryDto>(totalCount, dtos);
    }
}
```

## Q5: Are there performance considerations and best practices for avoiding N+1 query problems?

### Answer:

**Yes! Here are the key performance considerations:**

### ✅ **Best Practices:**

1. **Context-Specific Includes**:
```csharp
// ✅ GOOD: Different includes for different contexts
protected override IQueryable<Order> ApplyListIncludes(IQueryable<Order> query)
{
    return query.Include(o => o.Customer); // Minimal for lists
}

protected override IQueryable<Order> ApplyDetailIncludes(IQueryable<Order> query)
{
    return query
        .Include(o => o.Customer)
        .Include(o => o.OrderItems)
            .ThenInclude(oi => oi.Product); // Full data for details
}
```

2. **Limit Collection Sizes**:
```csharp
// ✅ GOOD: Limit large collections
protected override IQueryable<Product> ApplyDetailIncludes(IQueryable<Product> query)
{
    return query
        .Include(p => p.Reviews.Take(10)) // Limit to 10 reviews
        .Include(p => p.ProductImages.Take(5)); // Limit to 5 images
}
```

3. **Use Projections for Read-Only Data**:
```csharp
// ✅ GOOD: Projection instead of includes
public async Task<List<OrderSummaryDto>> GetOrderSummariesAsync()
{
    var query = await EfRepository.GetQueryableAsync();
    
    return await AsyncExecuter.ToListAsync(
        query.Select(o => new OrderSummaryDto
        {
            Id = o.Id,
            OrderNumber = o.OrderNumber,
            CustomerName = o.Customer.Name, // No include needed
            TotalAmount = o.OrderItems.Sum(oi => oi.Quantity * oi.UnitPrice)
        }));
}
```

4. **Monitor Performance**:
```csharp
// ✅ GOOD: Performance monitoring
protected override void LogIncludePerformance(string operation, int entityCount, TimeSpan duration)
{
    base.LogIncludePerformance(operation, entityCount, duration);
    
    if (entityCount > 100 && duration.TotalMilliseconds > 500)
    {
        Logger.LogWarning(
            "Large dataset query in {Operation}: Consider optimization. " +
            "Entities: {EntityCount}, Duration: {Duration}ms",
            operation, entityCount, duration.TotalMilliseconds);
    }
}
```

### ❌ **Anti-Patterns to Avoid:**

1. **Loading Everything Always**:
```csharp
// ❌ BAD: Too many includes for list views
protected override IQueryable<Customer> ApplyListIncludes(IQueryable<Customer> query)
{
    return query
        .Include(c => c.Orders)
            .ThenInclude(o => o.OrderItems)
                .ThenInclude(oi => oi.Product); // Too much for a list!
}
```

2. **Deep Nested Includes Without Limits**:
```csharp
// ❌ BAD: Unlimited deep includes
protected override IQueryable<Category> ApplyDetailIncludes(IQueryable<Category> query)
{
    return query
        .Include(c => c.SubCategories)
            .ThenInclude(sc => sc.SubCategories)
                .ThenInclude(ssc => sc.SubCategories); // Potential infinite recursion!
}
```

3. **Not Using Projections for Large Datasets**:
```csharp
// ❌ BAD: Loading full entities when only summary needed
public async Task<List<ProductDto>> GetAllProductsAsync()
{
    var query = await EfRepository.GetQueryableAsync();
    var products = await AsyncExecuter.ToListAsync(
        query
            .Include(p => p.Category)
            .Include(p => p.ProductImages)
            .Include(p => p.Reviews)); // Loading everything!
    
    return MapEntitiesToDtos(products);
}
```

### 🚀 **Performance Optimization Summary:**

| Scenario | Recommended Approach | Avoid |
|----------|---------------------|-------|
| **List Views** | Minimal includes or projections | Deep nested includes |
| **Detail Views** | Context-specific includes with limits | Loading all related data |
| **Search Results** | Projections with only needed fields | Full entity loading |
| **Large Collections** | Use `.Take()` to limit size | Unlimited collection loading |
| **Hierarchical Data** | Limit depth (max 3-5 levels) | Infinite recursion |
| **Read-Only Data** | Use projections | Entity tracking |

The `MapperlyCrudAppService` with custom repository methods provides the proper ABP-compliant infrastructure to implement these patterns effectively while maintaining the benefits of Mapperly's compile-time mapping and ABP's layered architecture principles.

## Summary: ABP-Compliant Approach

✅ **Correct Architecture:**
- **Application Layer**: Uses `MapperlyCrudAppService` + custom repository interfaces
- **Domain Layer**: Defines repository interfaces with include-specific methods
- **EntityFrameworkCore Layer**: Implements EF Core includes in repository implementations
- **Mapperly**: Handles compile-time mapping in Application layer

❌ **Avoid These Anti-Patterns:**
- Adding EF Core dependencies directly to Application layer
- Using `.Include()` methods in Application Services
- Violating ABP's layered architecture principles

This approach ensures proper separation of concerns, maintainability, and follows ABP Framework best practices.
