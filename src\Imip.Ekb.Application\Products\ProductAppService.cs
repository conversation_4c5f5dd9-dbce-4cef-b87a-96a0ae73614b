using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Imip.Ekb.Permissions;
using Imip.Ekb.Services;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// Implementation of product application service using Mapperly for mapping
    /// Uses the MapperlyCrudAppService base class to eliminate code duplication
    /// </summary>
    [Authorize(EkbPermissions.Products.Default)]
    public class ProductAppService :
        MapperlyCrudAppService<
            Product,                     // The entity
            ProductDto,                  // Used to show products
            Guid,                        // Primary key type
            GetProductListDto,           // Used for paging/sorting/filtering
            CreateUpdateProductDto,      // Used to create/update a product
            ProductMapper>,              // The Mapperly mapper
        IProductAppService               // The interface
    {
        public ProductAppService(
            IRepository<Product, Guid> repository,
            ProductMapper productMapper)
            : base(repository, productMapper)
        {

            // Configure permissions for methods
            GetPolicyName = EkbPermissions.Products.Default;
            GetListPolicyName = EkbPermissions.Products.Default;
            CreatePolicyName = EkbPermissions.Products.Create;
            UpdatePolicyName = EkbPermissions.Products.Edit;
            DeletePolicyName = EkbPermissions.Products.Delete;
        }

        /// <summary>
        /// Override GetListAsync to implement custom filtering
        /// </summary>
        public override async Task<PagedResultDto<ProductDto>> GetListAsync(GetProductListDto input)
        {
            // Check if sorting field is valid, default to Name if not specified
            if (input.Sorting.IsNullOrWhiteSpace())
            {
                input.Sorting = nameof(Product.Name);
            }

            // Get paged result from repository with custom filtering
            var query = await Repository.GetQueryableAsync();

            // Apply filters
            if (!input.Filter.IsNullOrWhiteSpace())
            {
                query = query.Where(p =>
                    p.Name.Contains(input.Filter) ||
                    p.Description.Contains(input.Filter) ||
                    p.SKU.Contains(input.Filter));
            }

            if (input.IsActive.HasValue)
            {
                query = query.Where(p => p.IsActive == input.IsActive.Value);
            }

            if (input.MinPrice.HasValue)
            {
                query = query.Where(p => p.Price >= input.MinPrice.Value);
            }

            if (input.MaxPrice.HasValue)
            {
                query = query.Where(p => p.Price <= input.MaxPrice.Value);
            }

            // Get count
            var totalCount = await AsyncExecuter.CountAsync(query);

            // Apply sorting and paging
            query = ApplySorting(query, input);
            query = ApplyPaging(query, input);

            // Execute query and map to DTOs using Mapperly (via base class)
            var entities = await AsyncExecuter.ToListAsync(query);
            var dtos = MapEntitiesToDtos(entities);

            return new PagedResultDto<ProductDto>(totalCount, dtos);
        }

        /// <summary>
        /// Override CreateAsync to check if SKU is already in use
        /// </summary>
        [Authorize(EkbPermissions.Products.Create)]
        public override async Task<ProductDto> CreateAsync(CreateUpdateProductDto input)
        {
            // Check if SKU is already in use
            if (await IsSkuInUseAsync(input.SKU))
            {
                throw new ProductSkuAlreadyExistsException(input.SKU);
            }

            return await base.CreateAsync(input);
        }

        /// <summary>
        /// Override UpdateAsync to check if SKU is already in use by another product
        /// </summary>
        [Authorize(EkbPermissions.Products.Edit)]
        public override async Task<ProductDto> UpdateAsync(Guid id, CreateUpdateProductDto input)
        {
            // Get the existing product
            var product = await Repository.GetAsync(id);

            // Check if SKU is already in use by another product
            if (product.SKU != input.SKU && await IsSkuInUseAsync(input.SKU))
            {
                throw new ProductSkuAlreadyExistsException(input.SKU);
            }

            return await base.UpdateAsync(id, input);
        }

        /// <summary>
        /// Checks if a SKU is already in use
        /// </summary>
        public async Task<bool> IsSkuInUseAsync(string sku)
        {
            var query = await Repository.GetQueryableAsync();
            return await AsyncExecuter.AnyAsync(query.Where(p => p.SKU == sku));
        }

        /// <summary>
        /// Updates the stock quantity of a product
        /// </summary>
        [Authorize(EkbPermissions.Products.Edit)]
        public async Task<ProductDto> UpdateStockQuantityAsync(Guid id, int newQuantity)
        {
            if (newQuantity < 0)
            {
                throw new ArgumentException("Stock quantity cannot be negative", nameof(newQuantity));
            }

            var product = await Repository.GetAsync(id);
            product.StockQuantity = newQuantity;
            await Repository.UpdateAsync(product);

            return MapEntityToDto(product);
        }

        #region Custom Mapperly Methods

        // Note: Basic CRUD mapping methods are now handled by the MapperlyCrudAppService base class
        // This eliminates code duplication across all Application Services

        /// <summary>
        /// Example method showing how to use enhanced mapping with custom logic
        /// Uses the GetMapper() helper method from base class to access the mapper
        /// </summary>
        public async Task<ProductDto> GetEnhancedProductAsync(Guid id)
        {
            var product = await Repository.GetAsync(id);
            return GetMapper().ToEnhancedDto(product);
        }

        /// <summary>
        /// Example method showing how to use simplified DTOs for list views
        /// Uses the GetMapper() helper method from base class to access the mapper
        /// </summary>
        public async Task<List<ProductListItemDto>> GetProductListItemsAsync()
        {
            var products = await Repository.GetListAsync();
            return GetMapper().ToListItemDto(products);
        }

        #endregion
    }
}
