using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Imip.Ekb.Mapping;
using Imip.Ekb.Permissions;
using Microsoft.AspNetCore.Authorization;
using Volo.Abp.Application.Dtos;
using Volo.Abp.Application.Services;
using Volo.Abp.Domain.Repositories;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// Implementation of product application service using Mapperly for mapping
    /// Demonstrates how to use Mapperly alongside AutoMapper in ABP Framework
    /// </summary>
    [Authorize(EkbPermissions.Products.Default)]
    public class ProductAppService :
        CrudAppService<
            Product,                     // The entity
            ProductDto,                  // Used to show products
            Guid,                        // Primary key type
            GetProductListDto,           // Used for paging/sorting/filtering
            CreateUpdateProductDto,      // Used to create a product
            CreateUpdateProductDto>,     // Used to update a product
        IProductAppService               // The interface
    {
        private readonly IRepository<Product, Guid> _productRepository;
        private readonly ProductMapper _productMapper;

        public ProductAppService(
            IRepository<Product, Guid> repository,
            ProductMapper productMapper)
            : base(repository)
        {
            _productRepository = repository;
            _productMapper = productMapper;

            // Configure permissions for methods
            GetPolicyName = EkbPermissions.Products.Default;
            GetListPolicyName = EkbPermissions.Products.Default;
            CreatePolicyName = EkbPermissions.Products.Create;
            UpdatePolicyName = EkbPermissions.Products.Edit;
            DeletePolicyName = EkbPermissions.Products.Delete;
        }

        /// <summary>
        /// Override GetListAsync to implement custom filtering
        /// </summary>
        public override async Task<PagedResultDto<ProductDto>> GetListAsync(GetProductListDto input)
        {
            // Check if sorting field is valid, default to Name if not specified
            if (input.Sorting.IsNullOrWhiteSpace())
            {
                input.Sorting = nameof(Product.Name);
            }

            // Get paged result from repository with custom filtering
            var query = await Repository.GetQueryableAsync();

            // Apply filters
            if (!input.Filter.IsNullOrWhiteSpace())
            {
                query = query.Where(p =>
                    p.Name.Contains(input.Filter) ||
                    p.Description.Contains(input.Filter) ||
                    p.SKU.Contains(input.Filter));
            }

            if (input.IsActive.HasValue)
            {
                query = query.Where(p => p.IsActive == input.IsActive.Value);
            }

            if (input.MinPrice.HasValue)
            {
                query = query.Where(p => p.Price >= input.MinPrice.Value);
            }

            if (input.MaxPrice.HasValue)
            {
                query = query.Where(p => p.Price <= input.MaxPrice.Value);
            }

            // Get count
            var totalCount = await AsyncExecuter.CountAsync(query);

            // Apply sorting and paging
            query = ApplySorting(query, input);
            query = ApplyPaging(query, input);

            // Execute query and map to DTOs using Mapperly
            var entities = await AsyncExecuter.ToListAsync(query);
            var dtos = _productMapper.ToDto(entities);

            return new PagedResultDto<ProductDto>(totalCount, dtos);
        }

        /// <summary>
        /// Override CreateAsync to check if SKU is already in use
        /// </summary>
        [Authorize(EkbPermissions.Products.Create)]
        public override async Task<ProductDto> CreateAsync(CreateUpdateProductDto input)
        {
            // Check if SKU is already in use
            if (await IsSkuInUseAsync(input.SKU))
            {
                throw new ProductSkuAlreadyExistsException(input.SKU);
            }

            return await base.CreateAsync(input);
        }

        /// <summary>
        /// Override UpdateAsync to check if SKU is already in use by another product
        /// </summary>
        [Authorize(EkbPermissions.Products.Edit)]
        public override async Task<ProductDto> UpdateAsync(Guid id, CreateUpdateProductDto input)
        {
            // Get the existing product
            var product = await Repository.GetAsync(id);

            // Check if SKU is already in use by another product
            if (product.SKU != input.SKU && await IsSkuInUseAsync(input.SKU))
            {
                throw new ProductSkuAlreadyExistsException(input.SKU);
            }

            return await base.UpdateAsync(id, input);
        }

        /// <summary>
        /// Checks if a SKU is already in use
        /// </summary>
        public async Task<bool> IsSkuInUseAsync(string sku)
        {
            var query = await Repository.GetQueryableAsync();
            return await AsyncExecuter.AnyAsync(query.Where(p => p.SKU == sku));
        }

        /// <summary>
        /// Updates the stock quantity of a product
        /// </summary>
        [Authorize(EkbPermissions.Products.Edit)]
        public async Task<ProductDto> UpdateStockQuantityAsync(Guid id, int newQuantity)
        {
            if (newQuantity < 0)
            {
                throw new ArgumentException("Stock quantity cannot be negative", nameof(newQuantity));
            }

            var product = await Repository.GetAsync(id);
            product.StockQuantity = newQuantity;
            await Repository.UpdateAsync(product);

            return _productMapper.ToDto(product);
        }

        #region Mapperly Override Methods

        /// <summary>
        /// Override MapToGetOutputDto to use Mapperly instead of AutoMapper
        /// </summary>
        protected override ProductDto MapToGetOutputDto(Product entity)
        {
            return _productMapper.ToDto(entity);
        }

        /// <summary>
        /// Override MapToGetListOutputDto to use Mapperly instead of AutoMapper
        /// </summary>
        protected override ProductDto MapToGetListOutputDto(Product entity)
        {
            return _productMapper.ToDto(entity);
        }

        /// <summary>
        /// Override MapToEntity for Create operations to use Mapperly instead of AutoMapper
        /// </summary>
        protected override Product MapToEntity(CreateUpdateProductDto createInput)
        {
            return _productMapper.ToEntity(createInput);
        }

        /// <summary>
        /// Override MapToEntity for Update operations to use Mapperly instead of AutoMapper
        /// </summary>
        protected override void MapToEntity(CreateUpdateProductDto updateInput, Product entity)
        {
            _productMapper.UpdateEntity(updateInput, entity);
        }

        /// <summary>
        /// Example method showing how to use enhanced mapping with custom logic
        /// </summary>
        public async Task<ProductDto> GetEnhancedProductAsync(Guid id)
        {
            var product = await Repository.GetAsync(id);
            return _productMapper.ToEnhancedDto(product);
        }

        /// <summary>
        /// Example method showing how to use simplified DTOs for list views
        /// </summary>
        public async Task<List<ProductListItemDto>> GetProductListItemsAsync()
        {
            var products = await Repository.GetListAsync();
            return _productMapper.ToListItemDto(products);
        }

        #endregion
    }
}
