import './index.css';
import { createRoot } from "react-dom/client";
import { createInertiaApp } from "@inertiajs/react";
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import { router } from "@inertiajs/react";

// Declare global variable for TypeScript
declare global {
    interface Window {
        __RequestVerificationToken?: string;
    }
}

const appName = window.document.getElementsByTagName("title")[0]?.innerText || "Inertia";

// Configure Inertia to include CSRF token in all requests
router.on('before', (event) => {
    const token = window.__RequestVerificationToken;
    if (token && event.detail.visit.method !== 'get') {
        // Add the antiforgery token to the headers
        event.detail.visit.headers = {
            ...event.detail.visit.headers,
            'RequestVerificationToken': token,
        };
    }
});

createInertiaApp({
    title: (title) => `${title} - ${appName}`,
    resolve: (name) => resolvePageComponent(`./Pages/${name}.tsx`, import.meta.glob("./Pages/**/*.tsx")),
    setup({ el, App, props }) {
        const root = createRoot(el);
        root.render(<App {...props} />);
    },
}).catch(console.error);