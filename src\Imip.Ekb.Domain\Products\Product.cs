using System;
using System.Diagnostics.CodeAnalysis;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Products
{
    /// <summary>
    /// Represents a product in the system
    /// </summary>
    public class Product : AuditedAggregateRoot<Guid>
    {
        /// <summary>
        /// Name of the product
        /// </summary>
        public required string Name { get; set; }

        /// <summary>
        /// Description of the product
        /// </summary>
        public required string Description { get; set; }

        /// <summary>
        /// Price of the product
        /// </summary>
        public decimal Price { get; set; }

        /// <summary>
        /// Quantity in stock
        /// </summary>
        public int StockQuantity { get; set; }

        /// <summary>
        /// SKU (Stock Keeping Unit) - unique identifier for the product
        /// </summary>
        public required string SKU { get; set; }

        /// <summary>
        /// Whether the product is active and available for sale
        /// </summary>
        public bool IsActive { get; set; }

        protected Product()
        {
            // Required by EF Core
        }

        [SetsRequiredMembers]
        public Product(
            Guid id,
            string name,
            string description,
            decimal price,
            int stockQuantity,
            string sku,
            bool isActive = true
        ) : base(id)
        {
            Name = name;
            Description = description;
            Price = price;
            StockQuantity = stockQuantity;
            SKU = sku;
            IsActive = isActive;
        }
    }
}
