# Mapperly Integration Guide for ABP Framework

This guide provides step-by-step instructions for integrating Riok.Mapperly alongside AutoMapper in ABP Framework projects, with a focus on CRUD operations.

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites](#prerequisites)
3. [Installation](#installation)
4. [Basic Setup](#basic-setup)
5. [Creating CRUD with Mapperly](#creating-crud-with-mapperly)
6. [Advanced Scenarios](#advanced-scenarios)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Overview

Riok.Mapperly is a compile-time object mapper that generates mapping code at build time, offering better performance and type safety compared to runtime mappers like AutoMapper. This guide shows how to use Mapperly alongside AutoMapper in ABP Framework without breaking existing functionality.

### Benefits of Mapperly

- **Compile-time mapping**: No runtime reflection overhead
- **Type safety**: Compile-time errors for mapping issues
- **Performance**: Generated code is optimized
- **Debugging**: Generated code is visible and debuggable
- **AOT compatible**: Works with Native AOT compilation

## Prerequisites

- ABP Framework 9.x or later
- .NET 9.0 or later
- Basic understanding of ABP Framework concepts (Entities, DTOs, Application Services)

## Installation

### Step 1: Add Riok.Mapperly NuGet Package

Add the Mapperly package to your Application layer:

```xml
<!-- In src/YourProject.Application/YourProject.Application.csproj -->
<PackageReference Include="Riok.Mapperly" Version="4.2.1" />
```

### Step 2: Create Mapping Infrastructure

Create the base infrastructure for ABP-compatible mappers:

```csharp
// src/YourProject.Application/Mapping/IAbpMapper.cs
public interface IAbpMapper<TEntity, TEntityDto, TKey, TCreateDto, TUpdateDto>
    where TEntity : class, IEntity<TKey>
    where TEntityDto : class, IEntityDto<TKey>
    where TCreateDto : class
    where TUpdateDto : class
{
    TEntityDto ToDto(TEntity entity);
    List<TEntityDto> ToDto(List<TEntity> entities);
    TEntity ToEntity(TCreateDto createDto);
    void UpdateEntity(TUpdateDto updateDto, TEntity entity);
    TEntity ToEntity(TCreateDto createDto, TKey id);
}
```

### Step 3: Register Mappers in DI Container

Update your Application Module:

```csharp
// src/YourProject.Application/YourProjectApplicationModule.cs
public override void ConfigureServices(ServiceConfigurationContext context)
{
    // Configure AutoMapper (existing functionality)
    Configure<AbpAutoMapperOptions>(options =>
    {
        options.AddMaps<YourProjectApplicationModule>();
    });

    // Configure Mapperly mappers (new functionality)
    ConfigureMapperlyMappers(context);
}

private void ConfigureMapperlyMappers(ServiceConfigurationContext context)
{
    // Register Mapperly mappers as singletons for better performance
    context.Services.AddSingleton<ProductMapper>();
    context.Services.AddSingleton<EmployeeMapper>();
    
    // Register as interfaces for dependency injection
    context.Services.AddSingleton<IAbpMapper<Product, ProductDto, Guid, CreateUpdateProductDto>>(
        provider => provider.GetRequiredService<ProductMapper>());
}
```

## Creating CRUD with Mapperly

### Step 1: Create the Entity

```csharp
// src/YourProject.Domain/Products/Product.cs
[SetsRequiredMembers]
public class Product : AuditedAggregateRoot<Guid>
{
    public required string Name { get; set; }
    public required string Description { get; set; }
    public decimal Price { get; set; }
    public int StockQuantity { get; set; }
    public required string SKU { get; set; }
    public bool IsActive { get; set; }

    protected Product() { }

    [SetsRequiredMembers]
    public Product(Guid id, string name, string description, decimal price, 
                   int stockQuantity, string sku, bool isActive = true) : base(id)
    {
        Name = name;
        Description = description;
        Price = price;
        StockQuantity = stockQuantity;
        SKU = sku;
        IsActive = isActive;
    }
}
```

### Step 2: Create DTOs

```csharp
// src/YourProject.Application.Contracts/Products/ProductDto.cs
public class ProductDto : AuditedEntityDto<Guid>
{
    public string Name { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public int StockQuantity { get; set; }
    public string SKU { get; set; } = string.Empty;
    public bool IsActive { get; set; }
}

// src/YourProject.Application.Contracts/Products/CreateUpdateProductDto.cs
public class CreateUpdateProductDto
{
    [Required]
    [StringLength(128, MinimumLength = 2)]
    public string Name { get; set; } = string.Empty;

    [StringLength(2000)]
    public string Description { get; set; } = string.Empty;

    [Required]
    [Range(0.01, 99999.99)]
    public decimal Price { get; set; }

    [Required]
    [Range(0, int.MaxValue)]
    public int StockQuantity { get; set; }

    [Required]
    [StringLength(32, MinimumLength = 3)]
    public string SKU { get; set; } = string.Empty;

    public bool IsActive { get; set; } = true;
}
```

### Step 3: Create Mapperly Mapper

```csharp
// src/YourProject.Application/Mapping/ProductMapper.cs
[Mapper]
public partial class ProductMapper : IAbpMapper<Product, ProductDto, Guid, CreateUpdateProductDto>
{
    /// <summary>
    /// Maps Product entity to ProductDto
    /// </summary>
    [MapperIgnoreSource(nameof(Product.ExtraProperties))]
    [MapperIgnoreSource(nameof(Product.ConcurrencyStamp))]
    public partial ProductDto ToDto(Product entity);

    /// <summary>
    /// Maps list of Product entities to list of ProductDto
    /// </summary>
    public partial List<ProductDto> ToDto(List<Product> entities);

    /// <summary>
    /// Creates a new Product entity from CreateUpdateProductDto
    /// </summary>
    public Product ToEntity(CreateUpdateProductDto createDto)
    {
        return new Product(
            Guid.NewGuid(),
            createDto.Name,
            createDto.Description,
            createDto.Price,
            createDto.StockQuantity,
            createDto.SKU,
            createDto.IsActive
        );
    }

    /// <summary>
    /// Creates a new Product entity with specified ID
    /// </summary>
    public Product ToEntity(CreateUpdateProductDto createDto, Guid id)
    {
        return new Product(
            id,
            createDto.Name,
            createDto.Description,
            createDto.Price,
            createDto.StockQuantity,
            createDto.SKU,
            createDto.IsActive
        );
    }

    /// <summary>
    /// Updates existing Product entity with data from CreateUpdateProductDto
    /// </summary>
    public void UpdateEntity(CreateUpdateProductDto updateDto, Product entity)
    {
        entity.Name = updateDto.Name;
        entity.Description = updateDto.Description;
        entity.Price = updateDto.Price;
        entity.StockQuantity = updateDto.StockQuantity;
        entity.SKU = updateDto.SKU;
        entity.IsActive = updateDto.IsActive;
    }
}
```

### Step 4: Create Application Service

```csharp
// src/YourProject.Application/Products/ProductAppService.cs
[Authorize(YourProjectPermissions.Products.Default)]
public class ProductAppService :
    CrudAppService<Product, ProductDto, Guid, GetProductListDto, CreateUpdateProductDto, CreateUpdateProductDto>,
    IProductAppService
{
    private readonly IRepository<Product, Guid> _productRepository;
    private readonly ProductMapper _productMapper;

    public ProductAppService(
        IRepository<Product, Guid> repository,
        ProductMapper productMapper) : base(repository)
    {
        _productRepository = repository;
        _productMapper = productMapper;

        // Configure permissions
        GetPolicyName = YourProjectPermissions.Products.Default;
        GetListPolicyName = YourProjectPermissions.Products.Default;
        CreatePolicyName = YourProjectPermissions.Products.Create;
        UpdatePolicyName = YourProjectPermissions.Products.Edit;
        DeletePolicyName = YourProjectPermissions.Products.Delete;
    }

    #region Mapperly Override Methods

    /// <summary>
    /// Override MapToGetOutputDto to use Mapperly instead of AutoMapper
    /// </summary>
    protected override ProductDto MapToGetOutputDto(Product entity)
    {
        return _productMapper.ToDto(entity);
    }

    /// <summary>
    /// Override MapToGetListOutputDto to use Mapperly instead of AutoMapper
    /// </summary>
    protected override ProductDto MapToGetListOutputDto(Product entity)
    {
        return _productMapper.ToDto(entity);
    }

    /// <summary>
    /// Override MapToEntity for Create operations to use Mapperly instead of AutoMapper
    /// </summary>
    protected override Product MapToEntity(CreateUpdateProductDto createInput)
    {
        return _productMapper.ToEntity(createInput);
    }

    /// <summary>
    /// Override MapToEntity for Update operations to use Mapperly instead of AutoMapper
    /// </summary>
    protected override void MapToEntity(CreateUpdateProductDto updateInput, Product entity)
    {
        _productMapper.UpdateEntity(updateInput, entity);
    }

    #endregion
}
```
