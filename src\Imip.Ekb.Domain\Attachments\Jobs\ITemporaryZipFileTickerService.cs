using System;
using System.Threading.Tasks;

namespace Imip.Ekb.Attachments.Jobs;

/// <summary>
/// Service for scheduling temporary ZIP file deletion using TickerQ
/// </summary>
public interface ITemporaryZipFileTickerService
{
    /// <summary>
    /// Schedules the deletion of a temporary ZIP file
    /// </summary>
    /// <param name="temporaryZipFileId">The ID of the temporary ZIP file</param>
    /// <param name="executionTime">The time when the deletion should be executed</param>
    /// <returns>A task representing the asynchronous operation</returns>
    Task ScheduleDeleteAsync(Guid temporaryZipFileId, DateTime executionTime);
}
