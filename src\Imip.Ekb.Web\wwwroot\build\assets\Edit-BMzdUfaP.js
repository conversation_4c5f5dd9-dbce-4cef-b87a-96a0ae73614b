import{v,j as e,Y as p}from"./App-Dy45zejD.js";import{B as c,C as g,a as b,b as f,c as y}from"./card-D9JhgxcF.js";import{I as n,S as E,a as C,b as D,c as F,d}from"./select-ZRFupiEN.js";import{A as S,a as P,L as i,S as L}from"./alert-DbmVDqVT.js";import{A}from"./arrow-left-D2mpE_8W.js";const k=({employee:t,departments:j,errors:m,error:o})=>{const{data:r,setData:l,post:N,processing:h}=v({firstName:t.firstName,lastName:t.lastName,email:t.email,phoneNumber:t.phoneNumber||"",department:t.department,position:t.position,hireDate:t.hireDate.split("T")[0],salary:t.salary,photoPath:t.photoPath||""}),u=s=>{s.preventDefault(),N(`/employees/${t.id}/edit`)},a=s=>{var x;return(x=m==null?void 0:m[s])==null?void 0:x[0]};return e.jsxs("div",{className:"container mx-auto p-6 max-w-4xl",children:[e.jsxs("div",{className:"flex items-center gap-4 mb-6",children:[e.jsx(p,{href:"/employees",children:e.jsxs(c,{variant:"outline",size:"sm",children:[e.jsx(A,{className:"w-4 h-4 mr-2"}),"Back to Employees"]})}),e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Edit Employee"}),e.jsxs("p",{className:"text-gray-600 mt-1",children:["Update ",t.firstName," ",t.lastName,"'s information"]})]})]}),o&&e.jsx(S,{className:"mb-6",variant:"destructive",children:e.jsx(P,{children:o})}),e.jsxs(g,{children:[e.jsx(b,{children:e.jsx(f,{children:"Employee Information"})}),e.jsx(y,{children:e.jsxs("form",{onSubmit:u,className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"firstName",children:"First Name *"}),e.jsx(n,{id:"firstName",value:r.firstName,onChange:s=>l("firstName",s.target.value),className:a("firstName")?"border-red-500":"",placeholder:"Enter first name"}),a("firstName")&&e.jsx("p",{className:"text-sm text-red-600",children:a("firstName")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"lastName",children:"Last Name *"}),e.jsx(n,{id:"lastName",value:r.lastName,onChange:s=>l("lastName",s.target.value),className:a("lastName")?"border-red-500":"",placeholder:"Enter last name"}),a("lastName")&&e.jsx("p",{className:"text-sm text-red-600",children:a("lastName")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"email",children:"Email *"}),e.jsx(n,{id:"email",type:"email",value:r.email,onChange:s=>l("email",s.target.value),className:a("email")?"border-red-500":"",placeholder:"Enter email address"}),a("email")&&e.jsx("p",{className:"text-sm text-red-600",children:a("email")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"phoneNumber",children:"Phone Number"}),e.jsx(n,{id:"phoneNumber",value:r.phoneNumber,onChange:s=>l("phoneNumber",s.target.value),className:a("phoneNumber")?"border-red-500":"",placeholder:"Enter phone number"}),a("phoneNumber")&&e.jsx("p",{className:"text-sm text-red-600",children:a("phoneNumber")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"department",children:"Department *"}),e.jsxs(E,{value:r.department,onValueChange:s=>l("department",s),children:[e.jsx(C,{className:a("department")?"border-red-500":"",children:e.jsx(D,{placeholder:"Select department"})}),e.jsxs(F,{children:[j.map(s=>e.jsx(d,{value:s,children:s},s)),e.jsx(d,{value:"Human Resources",children:"Human Resources"}),e.jsx(d,{value:"Engineering",children:"Engineering"}),e.jsx(d,{value:"Marketing",children:"Marketing"}),e.jsx(d,{value:"Sales",children:"Sales"}),e.jsx(d,{value:"Finance",children:"Finance"}),e.jsx(d,{value:"Operations",children:"Operations"})]})]}),a("department")&&e.jsx("p",{className:"text-sm text-red-600",children:a("department")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"position",children:"Position *"}),e.jsx(n,{id:"position",value:r.position,onChange:s=>l("position",s.target.value),className:a("position")?"border-red-500":"",placeholder:"Enter job position"}),a("position")&&e.jsx("p",{className:"text-sm text-red-600",children:a("position")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"hireDate",children:"Hire Date *"}),e.jsx(n,{id:"hireDate",type:"date",value:r.hireDate,onChange:s=>l("hireDate",s.target.value),className:a("hireDate")?"border-red-500":""}),a("hireDate")&&e.jsx("p",{className:"text-sm text-red-600",children:a("hireDate")})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"salary",children:"Salary *"}),e.jsx(n,{id:"salary",type:"number",step:"0.01",min:"0",value:r.salary,onChange:s=>l("salary",parseFloat(s.target.value)||0),className:a("salary")?"border-red-500":"",placeholder:"Enter salary amount"}),a("salary")&&e.jsx("p",{className:"text-sm text-red-600",children:a("salary")})]})]}),e.jsxs("div",{className:"space-y-2",children:[e.jsx(i,{htmlFor:"photoPath",children:"Photo URL"}),e.jsx(n,{id:"photoPath",value:r.photoPath,onChange:s=>l("photoPath",s.target.value),className:a("photoPath")?"border-red-500":"",placeholder:"Enter photo URL (optional)"}),a("photoPath")&&e.jsx("p",{className:"text-sm text-red-600",children:a("photoPath")})]}),e.jsxs("div",{className:"flex justify-end gap-4 pt-6 border-t",children:[e.jsx(p,{href:"/employees",children:e.jsx(c,{type:"button",variant:"outline",children:"Cancel"})}),e.jsxs(c,{type:"submit",disabled:h,children:[e.jsx(L,{className:"w-4 h-4 mr-2"}),h?"Updating...":"Update Employee"]})]})]})})]})]})};export{k as default};
