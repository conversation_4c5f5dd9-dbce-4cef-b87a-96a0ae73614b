using System;
using System.ComponentModel.DataAnnotations;

namespace Imip.Ekb.Attachments;

/// <summary>
/// DTO for file upload with file content
/// </summary>
public class FileUploadInputDto
{
    /// <summary>
    /// Optional description of the file
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Optional reference ID (e.g., reservation ID, guest ID) that this file is associated with
    /// </summary>
    public Guid? ReferenceId { get; set; }

    /// <summary>
    /// Optional reference type (e.g., "Reservation", "Guest") that this file is associated with
    /// </summary>
    [StringLength(50)]
    public string? ReferenceType { get; set; }

    /// <summary>
    /// The name of the file
    /// </summary>
    [Required]
    public required string FileName { get; set; }

    /// <summary>
    /// The content type of the file
    /// </summary>
    [Required]
    public required string ContentType { get; set; }

    /// <summary>
    /// The file content as a byte array
    /// </summary>
    [Required]
    public required byte[] FileContent { get; set; }
}
